namespace stage.Models
{
    public enum TypeAction
    {
        Creation = 0,
        Modification = 1,
        Suppression = 2,
        Consultation = 3,
        Connexion = 4,
        Deconnexion = 5,
        TentativeConnexionEchouee = 6,
        ChangementStatut = 7,
        Upload = 8,
        Download = 9,
        Export = 10,
        Import = 11
    }

    public enum TypeEntite
    {
        Utilisateur = 0,
        Societe = 1,
        Client = 2,
        Fournisseur = 3,
        FactureAchat = 4,
        FactureVente = 5,
        Document = 6,
        RetenueSource = 7,
        Notification = 8,
        Systeme = 9
    }

    public class AuditLog
    {
        public Guid Id { get; set; }
        public TypeAction Action { get; set; }
        public TypeEntite TypeEntite { get; set; }
        public Guid? EntiteId { get; set; }
        public string? NomEntite { get; set; }
        public string? ValeurAvant { get; set; } // JSON des valeurs avant modification
        public string? ValeurApres { get; set; } // JSON des valeurs après modification
        public string? Details { get; set; }
        public DateTime DateAction { get; set; } = DateTime.UtcNow;
        public string? AdresseIP { get; set; }
        public string? UserAgent { get; set; }

        // Relations
        public Guid? UtilisateurId { get; set; }
        public Applicationuser? Utilisateur { get; set; }
        public Guid SocieteId { get; set; }
        public Societe Societe { get; set; }
    }
}
