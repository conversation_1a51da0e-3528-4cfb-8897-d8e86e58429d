using System.ComponentModel.DataAnnotations;

namespace stage.Models
{
   
    public class RetenueSource
    {
        public Guid Id { get; set; }
        
      public DateTime? DateScan { get; set; }

      [MaxLength(500)]
     public string? CheminFichier { get; set; }
        
        [Required]
        public Guid SocieteId { get; set; }
        public Societe Societe { get; set; }
        
        [Required]
        public Guid UtilisateurId { get; set; }
        public Applicationuser Utilisateur { get; set; }
        
        public string? Commentaires { get; set; }
    }
}
