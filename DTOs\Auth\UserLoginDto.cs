﻿using System.ComponentModel.DataAnnotations;

namespace stage.DTOs.Auth
{
    public class UserLoginDto
    {
        [Required(ErrorMessage = "L'adresse email est requise.")]
        [EmailAddress(ErrorMessage = "Format d'adresse email invalide.")]
        public string Email { get; set; }

        [Required(ErrorMessage = "Le mot de passe est requis.")]
        public string MotDePasse { get; set; }
    }
}