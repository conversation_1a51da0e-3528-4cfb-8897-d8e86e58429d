using Microsoft.EntityFrameworkCore;
using stage.Models;

namespace stage.Repositories
{
    public class UtilisateurRepository : BaseRepository<Applicationuser>, IUtilisateurRepository
    {
        public UtilisateurRepository(StageContext context) : base(context)
        {
        }

        public async Task<Applicationuser?> GetByEmailAsync(string email)
        {
            return await _dbSet
                .Include(u => u.Societe)
                .FirstOrDefaultAsync(u => u.Email == email);
        }

        public async Task<IEnumerable<Applicationuser>> GetBySocieteIdAsync(Guid societeId)
        {
            return await _dbSet
                .Include(u => u.Societe)
                .Where(u => u.SocieteId == societeId)
                .ToListAsync();
        }

        public override async Task<Applicationuser?> GetByIdAsync(Guid id)
        {
            return await _dbSet
                .Include(u => u.Societe)
                .FirstOrDefaultAsync(u => u.Id == id);
        }
    }
}
