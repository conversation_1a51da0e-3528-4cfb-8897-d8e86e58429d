using stage.Models;
using stage.Repositories;
using System.Text.Json;
using System.Reflection;

namespace stage.Services
{
    public class AuditService : IAuditService
    {
        private readonly IAuditLogRepository _auditLogRepository;
        private readonly ILogger<AuditService> _logger;

        public AuditService(IAuditLogRepository auditLogRepository, ILogger<AuditService> logger)
        {
            _auditLogRepository = auditLogRepository;
            _logger = logger;
        }

        public async Task LogActionAsync(TypeAction action, TypeEntite typeEntite, Guid? entiteId, string? nomEntite, Guid? utilisateurId, Guid societeId, string? details = null, object? valeurAvant = null, object? valeurApres = null, string? adresseIP = null, string? userAgent = null)
        {
            try
            {
                var auditLog = new AuditLog
                {
                    Id = Guid.NewGuid(),
                    Action = action,
                    TypeEntite = typeEntite,
                    EntiteId = entiteId,
                    NomEntite = nomEntite,
                    UtilisateurId = utilisateurId,
                    SocieteId = societeId,
                    Details = details,
                    ValeurAvant = valeurAvant != null ? JsonSerializer.Serialize(valeurAvant) : null,
                    ValeurApres = valeurApres != null ? JsonSerializer.Serialize(valeurApres) : null,
                    AdresseIP = adresseIP,
                    UserAgent = userAgent,
                    DateAction = DateTime.UtcNow
                };

                await _auditLogRepository.AddAsync(auditLog);
                await _auditLogRepository.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de l'enregistrement de l'audit log");
            }
        }

        public async Task LogCreationAsync<T>(T entite, Guid utilisateurId, Guid societeId, string? adresseIP = null, string? userAgent = null) where T : class
        {
            var typeEntite = GetTypeEntite<T>();
            var entiteId = GetEntityId(entite);
            var nomEntite = GetEntityName(entite);

            await LogActionAsync(
                TypeAction.Creation,
                typeEntite,
                entiteId,
                nomEntite,
                utilisateurId,
                societeId,
                $"Création de {typeof(T).Name}",
                null,
                entite,
                adresseIP,
                userAgent
            );
        }

        public async Task LogModificationAsync<T>(T entiteAvant, T entiteApres, Guid utilisateurId, Guid societeId, string? adresseIP = null, string? userAgent = null) where T : class
        {
            var typeEntite = GetTypeEntite<T>();
            var entiteId = GetEntityId(entiteApres);
            var nomEntite = GetEntityName(entiteApres);

            await LogActionAsync(
                TypeAction.Modification,
                typeEntite,
                entiteId,
                nomEntite,
                utilisateurId,
                societeId,
                $"Modification de {typeof(T).Name}",
                entiteAvant,
                entiteApres,
                adresseIP,
                userAgent
            );
        }

        public async Task LogSuppressionAsync<T>(T entite, Guid utilisateurId, Guid societeId, string? adresseIP = null, string? userAgent = null) where T : class
        {
            var typeEntite = GetTypeEntite<T>();
            var entiteId = GetEntityId(entite);
            var nomEntite = GetEntityName(entite);

            await LogActionAsync(
                TypeAction.Suppression,
                typeEntite,
                entiteId,
                nomEntite,
                utilisateurId,
                societeId,
                $"Suppression de {typeof(T).Name}",
                entite,
                null,
                adresseIP,
                userAgent
            );
        }

        public async Task LogConnexionAsync(Guid utilisateurId, Guid societeId, bool succes, string? adresseIP = null, string? userAgent = null)
        {
            var action = succes ? TypeAction.Connexion : TypeAction.TentativeConnexionEchouee;
            var details = succes ? "Connexion réussie" : "Tentative de connexion échouée";

            await LogActionAsync(
                action,
                TypeEntite.Utilisateur,
                utilisateurId,
                "Connexion utilisateur",
                utilisateurId,
                societeId,
                details,
                null,
                null,
                adresseIP,
                userAgent
            );
        }

        public async Task LogConsultationAsync(TypeEntite typeEntite, Guid entiteId, string nomEntite, Guid utilisateurId, Guid societeId, string? adresseIP = null, string? userAgent = null)
        {
            await LogActionAsync(
                TypeAction.Consultation,
                typeEntite,
                entiteId,
                nomEntite,
                utilisateurId,
                societeId,
                $"Consultation de {nomEntite}",
                null,
                null,
                adresseIP,
                userAgent
            );
        }

        public async Task<IEnumerable<AuditLog>> GetAuditTrailAsync(Guid societeId, DateTime? dateDebut = null, DateTime? dateFin = null)
        {
            if (dateDebut.HasValue && dateFin.HasValue)
            {
                return await _auditLogRepository.GetByDateRangeAsync(dateDebut.Value, dateFin.Value);
            }
            
            return await _auditLogRepository.GetBySocieteIdAsync(societeId);
        }

        public async Task<IEnumerable<AuditLog>> GetEntityHistoryAsync(TypeEntite typeEntite, Guid entiteId)
        {
            return await _auditLogRepository.GetByEntityAsync(typeEntite, entiteId);
        }

        private TypeEntite GetTypeEntite<T>() where T : class
        {
            return typeof(T).Name switch
            {
                nameof(Applicationuser) => TypeEntite.Utilisateur,
                nameof(Societe) => TypeEntite.Societe,
                nameof(Client) => TypeEntite.Client,
                nameof(Fournisseur) => TypeEntite.Fournisseur,
                nameof(FactureAchat) => TypeEntite.FactureAchat,
                nameof(FactureVente) => TypeEntite.FactureVente,
                nameof(Document) => TypeEntite.Document,
                nameof(RetenueSource) => TypeEntite.RetenueSource,
                nameof(Notification) => TypeEntite.Notification,
                _ => TypeEntite.Systeme
            };
        }

        private Guid? GetEntityId(object entite)
        {
            var idProperty = entite.GetType().GetProperty("Id");
            if (idProperty != null && idProperty.PropertyType == typeof(Guid))
            {
                return (Guid?)idProperty.GetValue(entite);
            }
            return null;
        }

        private string? GetEntityName(object entite)
        {
            // Essayer de récupérer une propriété "Nom" ou "Titre" ou "Numero"
            var nomProperty = entite.GetType().GetProperty("Nom") ?? 
                             entite.GetType().GetProperty("Titre") ?? 
                             entite.GetType().GetProperty("Numero");
            
            if (nomProperty != null)
            {
                return nomProperty.GetValue(entite)?.ToString();
            }
            
            return entite.GetType().Name;
        }
    }
}
