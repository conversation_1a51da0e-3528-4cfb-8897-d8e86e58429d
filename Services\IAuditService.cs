using stage.Models;

namespace stage.Services
{
    public interface IAuditService
    {
        Task LogActionAsync(TypeAction action, TypeEntite typeEntite, Guid? entiteId, string? nomEntite, Guid? utilisateurId, Guid societeId, string? details = null, object? valeurAvant = null, object? valeurApres = null, string? adresseIP = null, string? userAgent = null);
        Task LogCreationAsync<T>(T entite, Guid utilisateurId, Guid societeId, string? adresseIP = null, string? userAgent = null) where T : class;
        Task LogModificationAsync<T>(T entiteAvant, T entiteApres, Guid utilisateurId, Guid societeId, string? adresseIP = null, string? userAgent = null) where T : class;
        Task LogSuppressionAsync<T>(T entite, Guid utilisateurId, Guid societeId, string? adresseIP = null, string? userAgent = null) where T : class;
        Task LogConnexionAsync(Guid utilisateurId, Guid societeId, bool succes, string? adresseIP = null, string? userAgent = null);
        Task LogConsultationAsync(TypeEntite typeEntite, Guid entiteId, string nomEntite, Guid utilisateurId, Guid societeId, string? adresseIP = null, string? userAgent = null);
        Task<IEnumerable<AuditLog>> GetAuditTrailAsync(Guid societeId, DateTime? dateDebut = null, DateTime? dateFin = null);
        Task<IEnumerable<AuditLog>> GetEntityHistoryAsync(TypeEntite typeEntite, Guid entiteId);
    }
}
