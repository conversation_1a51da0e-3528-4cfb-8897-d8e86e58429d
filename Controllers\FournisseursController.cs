﻿using Microsoft.AspNetCore.Mvc;
using stage.Models;
using stage.Repositories;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;

namespace stage.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize] 
    public class FournisseursController : ControllerBase
    {
        private readonly IFournisseurRepository _fournisseurRepository;

        public FournisseursController(IFournisseurRepository fournisseurRepository)
        {
            _fournisseurRepository = fournisseurRepository;
        }

        private Guid GetUserSocieteId()
        {
            var societeIdClaim = User.FindFirst("SocieteId");
            if (societeIdClaim == null || !Guid.TryParse(societeIdClaim.Value, out var societeId))
            {
                throw new InvalidOperationException("SocieteId claim missing or invalid in JWT.");
            }
            return societeId;
        }

       
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Fournisseur>>> GetFournisseurs()
        {
            var societeId = GetUserSocieteId();
            var fournisseurs = await _fournisseurRepository.FindAsync(f => f.SocieteId == societeId);
            return Ok(fournisseurs);
        }

      
        [HttpGet("{id}")]
        public async Task<ActionResult<Fournisseur>> GetFournisseur(Guid id)
        {
            var societeId = GetUserSocieteId();
            var fournisseur = await _fournisseurRepository.GetByIdAsync(id);

            if (fournisseur == null || fournisseur.SocieteId != societeId)
            {
                return NotFound();
            }

            return Ok(fournisseur);
        }

    
        [HttpPost]
        public async Task<ActionResult<Fournisseur>> PostFournisseur([FromBody] Fournisseur fournisseur)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var societeId = GetUserSocieteId();
            fournisseur.Id = Guid.NewGuid();
            fournisseur.SocieteId = societeId; 

            await _fournisseurRepository.AddAsync(fournisseur);
            await _fournisseurRepository.SaveChangesAsync();

            return CreatedAtAction(nameof(GetFournisseur), new { id = fournisseur.Id }, fournisseur);
        }
       
        [HttpPut("{id}")]
        public async Task<IActionResult> PutFournisseur(Guid id, [FromBody] Fournisseur fournisseur)
        {
            if (id != fournisseur.Id || !ModelState.IsValid)
            {
                return BadRequest();
            }

            var societeId = GetUserSocieteId();
            var existingFournisseur = await _fournisseurRepository.GetByIdAsync(id);

            if (existingFournisseur == null || existingFournisseur.SocieteId != societeId)
            {
                return NotFound();
            }

            existingFournisseur.Nom = fournisseur.Nom;
            existingFournisseur.Email = fournisseur.Email;
            existingFournisseur.Adresse = fournisseur.Adresse;


            _fournisseurRepository.Update(existingFournisseur);

            try
            {
                await _fournisseurRepository.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!await FournisseurExists(id, societeId))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteFournisseur(Guid id)
        {
            var societeId = GetUserSocieteId();
            var fournisseur = await _fournisseurRepository.GetByIdAsync(id);

            if (fournisseur == null || fournisseur.SocieteId != societeId)
            {
                return NotFound();
            }

            _fournisseurRepository.Remove(fournisseur);
            await _fournisseurRepository.SaveChangesAsync();

            return NoContent();
        }

        private async Task<bool> FournisseurExists(Guid id, Guid societeId)
        {
            return (await _fournisseurRepository.FindAsync(f => f.Id == id && f.SocieteId == societeId)).Any();
        }
    }
}