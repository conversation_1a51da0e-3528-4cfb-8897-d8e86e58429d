using Microsoft.AspNetCore.Mvc;
using stage.Models;
using stage.Repositories;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;

namespace stage.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize(Roles = "Admin")] 
    public class SocietesController : ControllerBase
    {
        private readonly ISocieteRepository _societeRepository;

        public SocietesController(ISocieteRepository societeRepository)
        {
            _societeRepository = societeRepository;
        }

        private Guid GetUserSocieteId()
        {
            var societeIdClaim = User.FindFirst("SocieteId");
            if (societeIdClaim == null || !Guid.TryParse(societeIdClaim.Value, out var societeId))
            {
                throw new InvalidOperationException("SocieteId claim missing or invalid in JWT.");
            }
            return societeId;
        }
        [HttpGet]
        public async Task<ActionResult<Societe>> GetSociete()
        {
            var societeId = GetUserSocieteId();
            var societe = await _societeRepository.GetByIdAsync(societeId);

            if (societe == null)
            {
                return NotFound("Société non trouvée.");
            }

            return Ok(societe);
        }

        [HttpPut]
        public async Task<IActionResult> PutSociete([FromBody] Societe societe)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var societeId = GetUserSocieteId();
            var existingSociete = await _societeRepository.GetByIdAsync(societeId);

            if (existingSociete == null)
            {
                return NotFound("Société non trouvée.");
            }

            existingSociete.Nom = societe.Nom;
            existingSociete.Adresse = societe.Adresse;
            existingSociete.MatriculeFiscale = societe.MatriculeFiscale;
            existingSociete.Logo = societe.Logo;
            existingSociete.Signature = societe.Signature;
            existingSociete.Cachet = societe.Cachet;

            _societeRepository.Update(existingSociete);

            try
            {
                await _societeRepository.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!await SocieteExists(societeId))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        private async Task<bool> SocieteExists(Guid id)
        {
            var societe = await _societeRepository.GetByIdAsync(id);
            return societe != null;
        }
    }
}
