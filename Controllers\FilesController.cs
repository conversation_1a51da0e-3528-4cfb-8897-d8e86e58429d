using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using stage.Services;
using stage.Models;
using System.Security.Claims;

namespace stage.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class FilesController : ControllerBase
    {
        private readonly IFileService _fileService;
        private readonly IAuditService _auditService;
        private readonly StageContext _context;

        public FilesController(IFileService fileService, IAuditService auditService, StageContext context)
        {
            _fileService = fileService;
            _auditService = auditService;
            _context = context;
        }

        private Guid GetUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
            {
                throw new InvalidOperationException("UserId claim missing or invalid in JWT.");
            }
            return userId;
        }

        private Guid GetUserSocieteId()
        {
            var societeIdClaim = User.FindFirst("SocieteId");
            if (societeIdClaim == null || !Guid.TryParse(societeIdClaim.Value, out var societeId))
            {
                throw new InvalidOperationException("SocieteId claim missing or invalid in JWT.");
            }
            return societeId;
        }

        private string GetUserIP()
        {
            return HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
        }

        private string GetUserAgent()
        {
            return HttpContext.Request.Headers["User-Agent"].ToString();
        }

        [HttpGet("retenue/{id}")]
        public async Task<IActionResult> DownloadRetenueFile(Guid id)
        {
            var societeId = GetUserSocieteId();
            var userId = GetUserId();

            var retenue = await _context.RetenuesSource
                .FirstOrDefaultAsync(r => r.Id == id && r.SocieteId == societeId);

            if (retenue == null)
                return NotFound("Retenue à la source non trouvée");

            if (string.IsNullOrEmpty(retenue.CheminFichier) || !await _fileService.FileExistsAsync(retenue.CheminFichier))
                return NotFound("Fichier non trouvé");

            try
            {
                var fileBytes = await _fileService.GetFileAsync(retenue.CheminFichier);
                var contentType = _fileService.GetContentType(retenue.CheminFichier);
                var fileName = Path.GetFileName(retenue.CheminFichier);

                // Log du téléchargement
                await _auditService.LogActionAsync(
                    TypeAction.Download,
                    TypeEntite.RetenueSource,
                    retenue.Id,
                    "Fichier retenue à la source",
                    userId,
                    societeId,
                    "Téléchargement fichier retenue",
                    null,
                    null,
                    GetUserIP(),
                    GetUserAgent()
                );

                return File(fileBytes, contentType, fileName);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erreur lors du téléchargement: {ex.Message}");
            }
        }



        [HttpPost("upload")]
        public async Task<IActionResult> UploadFile(IFormFile file, [FromQuery] string? subfolder = "uploads")
        {
            var userId = GetUserId();
            var societeId = GetUserSocieteId();

            if (file == null || file.Length == 0)
                return BadRequest("Aucun fichier fourni");

            if (!await _fileService.IsValidFileType(file.FileName))
                return BadRequest("Type de fichier non autorisé");

            try
            {
                var filePath = await _fileService.SaveFileAsync(file, subfolder);

                // Log de l'upload
                await _auditService.LogActionAsync(
                    TypeAction.Upload,
                    TypeEntite.Systeme,
                    null,
                    file.FileName,
                    userId,
                    societeId,
                    $"Upload fichier: {file.FileName}",
                    null,
                    new { FileName = file.FileName, Size = file.Length, Path = filePath },
                    GetUserIP(),
                    GetUserAgent()
                );

                return Ok(new
                {
                    Message = "Fichier uploadé avec succès",
                    FilePath = filePath,
                    FileName = file.FileName,
                    Size = file.Length
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erreur lors de l'upload: {ex.Message}");
            }
        }

        [HttpGet("info")]
        public async Task<IActionResult> GetFileInfo([FromQuery] string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                return BadRequest("Chemin de fichier requis");

            if (!await _fileService.FileExistsAsync(filePath))
                return NotFound("Fichier non trouvé");

            try
            {
                var size = await _fileService.GetFileSizeAsync(filePath);
                var contentType = _fileService.GetContentType(filePath);
                var fileName = Path.GetFileName(filePath);

                return Ok(new
                {
                    FileName = fileName,
                    Size = size,
                    ContentType = contentType,
                    Path = filePath
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erreur lors de la récupération des informations: {ex.Message}");
            }
        }
    }
}
