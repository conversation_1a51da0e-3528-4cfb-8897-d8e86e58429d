using Microsoft.EntityFrameworkCore;
using stage.Models;

namespace stage.Repositories
{
    public class AuditLogRepository : BaseRepository<AuditLog>, IAuditLogRepository
    {
        public AuditLogRepository(StageContext context) : base(context)
        {
        }

        public async Task<IEnumerable<AuditLog>> GetByUtilisateurIdAsync(Guid utilisateurId)
        {
            return await _dbSet
                .Include(al => al.Utilisateur)
                .Include(al => al.Societe)
                .Where(al => al.UtilisateurId == utilisateurId)
                .OrderByDescending(al => al.DateAction)
                .ToListAsync();
        }

        public async Task<IEnumerable<AuditLog>> GetBySocieteIdAsync(Guid societeId)
        {
            return await _dbSet
                .Include(al => al.Utilisateur)
                .Include(al => al.Societe)
                .Where(al => al.SocieteId == societeId)
                .OrderByDescending(al => al.DateAction)
                .ToListAsync();
        }

        public async Task<IEnumerable<AuditLog>> GetByEntityAsync(TypeEntite typeEntite, Guid entiteId)
        {
            return await _dbSet
                .Include(al => al.Utilisateur)
                .Include(al => al.Societe)
                .Where(al => al.TypeEntite == typeEntite && al.EntiteId == entiteId)
                .OrderByDescending(al => al.DateAction)
                .ToListAsync();
        }

        public async Task<IEnumerable<AuditLog>> GetByActionAsync(TypeAction action)
        {
            return await _dbSet
                .Include(al => al.Utilisateur)
                .Include(al => al.Societe)
                .Where(al => al.Action == action)
                .OrderByDescending(al => al.DateAction)
                .ToListAsync();
        }

        public async Task<IEnumerable<AuditLog>> GetByDateRangeAsync(DateTime dateDebut, DateTime dateFin)
        {
            return await _dbSet
                .Include(al => al.Utilisateur)
                .Include(al => al.Societe)
                .Where(al => al.DateAction >= dateDebut && al.DateAction <= dateFin)
                .OrderByDescending(al => al.DateAction)
                .ToListAsync();
        }

        public async Task<IEnumerable<AuditLog>> GetRecentActivityAsync(Guid societeId, int count = 50)
        {
            return await _dbSet
                .Include(al => al.Utilisateur)
                .Include(al => al.Societe)
                .Where(al => al.SocieteId == societeId)
                .OrderByDescending(al => al.DateAction)
                .Take(count)
                .ToListAsync();
        }
    }
}
