﻿using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using stage.Models;

namespace stage.Models
{
    public class StageContext : IdentityDbContext<Applicationuser, IdentityRole<Guid>, Guid>
    {

        public StageContext(DbContextOptions<StageContext> options) : base(options) { }

        public DbSet<Societe> Societes { get; set; }
        public DbSet<Client> Clients { get; set; }
        public DbSet<Fournisseur> Fournisseurs { get; set; }
        public DbSet<FactureAchat> FacturesAchat { get; set; }
        public DbSet<FactureVente> FacturesVente { get; set; }
        public DbSet<Document> Documents { get; set; }
        public DbSet<TypeDocument> TypeDocuments { get; set; }
        public DbSet<RetenueSource> RetenuesSource { get; set; }
        public DbSet<Notification> Notifications { get; set; }
        public DbSet<NotificationPreference> NotificationPreferences { get; set; }
        public DbSet<AuditLog> AuditLogs { get; set; }
        public DbSet<ProfilUtilisateur> ProfilsUtilisateurs { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            modelBuilder.Entity<Applicationuser>().ToTable("Users");
            modelBuilder.Entity<IdentityRole<Guid>>().ToTable("Roles");

            modelBuilder.Entity<Societe>()
                .HasIndex(s => s.MatriculeFiscale)
                .IsUnique();

            modelBuilder.Entity<Applicationuser>()
                .HasOne(u => u.Societe)
                .WithMany(s => s.Utilisateurs)
                .HasForeignKey(u => u.SocieteId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Client>()
                .HasOne(c => c.Societe)
                .WithMany(s => s.Clients)
                .HasForeignKey(c => c.SocieteId)
                .OnDelete(DeleteBehavior.Cascade);
           
            modelBuilder.Entity<Fournisseur>()
                .HasOne(f => f.Societe)
                .WithMany(s => s.Fournisseurs)
                .HasForeignKey(f => f.SocieteId)
                .OnDelete(DeleteBehavior.Cascade); 


            modelBuilder.Entity<FactureAchat>()
                .HasOne(f => f.Societe)
                .WithMany(s => s.FacturesAchat)
                .HasForeignKey(f => f.SocieteId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<FactureAchat>()
                .HasOne(f => f.Fournisseur)
                .WithMany()
                .HasForeignKey(f => f.FournisseurId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<FactureVente>()
                .HasOne(f => f.Societe)
                .WithMany(s => s.FacturesVente)
                .HasForeignKey(f => f.SocieteId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<FactureVente>()
                .HasOne(f => f.Client)
                .WithMany()
                .HasForeignKey(f => f.ClientId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Document>()
                .HasOne(d => d.TypeDocument)
                .WithMany(td => td.Documents)
                .HasForeignKey(d => d.TypeDocumentId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Restrict); 

           
            modelBuilder.Entity<Document>()
                .HasOne(d => d.Societe)
                .WithMany(s => s.Documents)
                .HasForeignKey(d => d.SocieteId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<TypeDocument>()
                .HasOne(td => td.Societe)
                .WithMany()
                .HasForeignKey(td => td.SocieteId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Cascade);

            // Configuration pour RetenueSource
            modelBuilder.Entity<RetenueSource>()
                .HasOne(r => r.Societe)
                .WithMany(s => s.RetenuesSource)
                .HasForeignKey(r => r.SocieteId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<RetenueSource>()
                .HasOne(r => r.Utilisateur)
                .WithMany()
                .HasForeignKey(r => r.UtilisateurId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Restrict);

            // Configuration pour Notification
            modelBuilder.Entity<Notification>()
                .HasOne(n => n.Utilisateur)
                .WithMany()
                .HasForeignKey(n => n.UtilisateurId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Notification>()
                .HasOne(n => n.Societe)
                .WithMany()
                .HasForeignKey(n => n.SocieteId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Cascade);

            // Configuration pour NotificationPreference
            modelBuilder.Entity<NotificationPreference>()
                .HasOne(np => np.Utilisateur)
                .WithMany()
                .HasForeignKey(np => np.UtilisateurId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Cascade);

            // Configuration pour AuditLog
            modelBuilder.Entity<AuditLog>()
                .HasOne(al => al.Utilisateur)
                .WithMany()
                .HasForeignKey(al => al.UtilisateurId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<AuditLog>()
                .HasOne(al => al.Societe)
                .WithMany()
                .HasForeignKey(al => al.SocieteId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Restrict);

            // Configuration pour ProfilUtilisateur
            modelBuilder.Entity<ProfilUtilisateur>()
                .HasOne(pu => pu.Utilisateur)
                .WithOne()
                .HasForeignKey<ProfilUtilisateur>(pu => pu.UtilisateurId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Cascade);

            // Configuration pour les nouvelles relations dans Document
            modelBuilder.Entity<Document>()
                .HasOne(d => d.UtilisateurDemandeur)
                .WithMany()
                .HasForeignKey(d => d.UtilisateurDemandeurId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Document>()
                .HasOne(d => d.UtilisateurApprobateur)
                .WithMany()
                .HasForeignKey(d => d.UtilisateurApprobateurId)
                .OnDelete(DeleteBehavior.SetNull);

            // Configuration pour les nouvelles relations dans Facture
            modelBuilder.Entity<FactureAchat>()
                .HasOne(f => f.UtilisateurCreation)
                .WithMany()
                .HasForeignKey(f => f.UtilisateurCreationId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<FactureVente>()
                .HasOne(f => f.UtilisateurCreation)
                .WithMany()
                .HasForeignKey(f => f.UtilisateurCreationId)
                .OnDelete(DeleteBehavior.SetNull);

            // Configuration pour les nouvelles relations dans Client et Fournisseur
            modelBuilder.Entity<Client>()
                .HasMany(c => c.FacturesVente)
                .WithOne(f => f.Client)
                .HasForeignKey(f => f.ClientId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Fournisseur>()
                .HasMany(f => f.FacturesAchat)
                .WithOne(fa => fa.Fournisseur)
                .HasForeignKey(fa => fa.FournisseurId)
                .OnDelete(DeleteBehavior.Restrict);

            base.OnModelCreating(modelBuilder);
        }
    }
}
