using Microsoft.AspNetCore.Mvc;
using stage.Models;
using stage.Repositories;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;

namespace stage.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize] 
    public class ClientsController : ControllerBase
    {
        private readonly IClientRepository _clientRepository;

        public ClientsController(IClientRepository clientRepository)
        {
            _clientRepository = clientRepository;
        }

        private Guid GetUserSocieteId()
        {
            var societeIdClaim = User.FindFirst("SocieteId");
            if (societeIdClaim == null || !Guid.TryParse(societeIdClaim.Value, out var societeId))
            {
                throw new InvalidOperationException("SocieteId claim missing or invalid in JWT.");
            }
            return societeId;
        }

       
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Client>>> GetClients()
        {
            var societeId = GetUserSocieteId();
            var clients = await _clientRepository.FindAsync(c => c.SocieteId == societeId);
            return Ok(clients);
        }

      
        [HttpGet("{id}")]
        public async Task<ActionResult<Client>> GetClient(Guid id)
        {
            var societeId = GetUserSocieteId();
            var client = await _clientRepository.GetByIdAsync(id);

            if (client == null || client.SocieteId != societeId)
            {
                return NotFound();
            }

            return Ok(client);
        }

    
        [HttpPost]
        public async Task<ActionResult<Client>> PostClient([FromBody] Client client)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var societeId = GetUserSocieteId();
            client.Id = Guid.NewGuid();
            client.SocieteId = societeId; 

            await _clientRepository.AddAsync(client);
            await _clientRepository.SaveChangesAsync();

            return CreatedAtAction(nameof(GetClient), new { id = client.Id }, client);
        }
       
        [HttpPut("{id}")]
        public async Task<IActionResult> PutClient(Guid id, [FromBody] Client client)
        {
            if (id != client.Id || !ModelState.IsValid)
            {
                return BadRequest();
            }

            var societeId = GetUserSocieteId();
            var existingClient = await _clientRepository.GetByIdAsync(id);

            if (existingClient == null || existingClient.SocieteId != societeId)
            {
                return NotFound();
            }

            existingClient.Nom = client.Nom;
            existingClient.Email = client.Email;
            existingClient.Adresse = client.Adresse;


            _clientRepository.Update(existingClient);

            try
            {
                await _clientRepository.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!await ClientExists(id, societeId))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteClient(Guid id)
        {
            var societeId = GetUserSocieteId();
            var client = await _clientRepository.GetByIdAsync(id);

            if (client == null || client.SocieteId != societeId)
            {
                return NotFound();
            }

            _clientRepository.Remove(client);
            await _clientRepository.SaveChangesAsync();

            return NoContent();
        }

        private async Task<bool> ClientExists(Guid id, Guid societeId)
        {
            return (await _clientRepository.FindAsync(c => c.Id == id && c.SocieteId == societeId)).Any();
        }
    }
}
