namespace stage.Models
{
    public class ProfilUtilisateur
    {
        public Guid Id { get; set; }
        public string? Telephone { get; set; }
        public string? TelephoneMobile { get; set; }
        public string? Adresse { get; set; }
        public string? Poste { get; set; }
        public string? Departement { get; set; }
        public DateTime? DateEmbauche { get; set; }
        public string? PhotoProfil { get; set; }
        public string? SignatureNumerique { get; set; }
        public string? CachetNumerique { get; set; }
        
        // Préférences de sécurité
        public bool DeuxFacteursActive { get; set; } = false;
        public int TimeoutSession { get; set; } = 30; // en minutes
        public bool AlertesConnexion { get; set; } = true;
        
        // Préférences d'interface
        public string? LanguePreferee { get; set; } = "fr";
        public string? ThemeInterface { get; set; } = "light";
        public string? FormatDate { get; set; } = "dd/MM/yyyy";
        public string? DevisePreferee { get; set; } = "TND";
        
        // Métadonnées
        public DateTime DateCreation { get; set; } = DateTime.UtcNow;
        public DateTime? DateModification { get; set; }
        
        // Relations
        public Guid UtilisateurId { get; set; }
        public Applicationuser Utilisateur { get; set; }
    }
}
