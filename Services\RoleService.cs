using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

namespace stage.Services
{
    public class RoleService : IRoleService
    {
        private readonly RoleManager<IdentityRole<Guid>> _roleManager;

        public const string ADMIN_ROLE = "Admin";
        public const string USER_ROLE = "User";

        public RoleService(RoleManager<IdentityRole<Guid>> roleManager)
        {
            _roleManager = roleManager;
        }

      
        public async Task InitializeDefaultRolesAsync()
        {
            if (!await _roleManager.RoleExistsAsync(ADMIN_ROLE))
            {
                await _roleManager.CreateAsync(new IdentityRole<Guid>(ADMIN_ROLE));
            }

            if (!await _roleManager.RoleExistsAsync(USER_ROLE))
            {
                await _roleManager.CreateAsync(new IdentityRole<Guid>(USER_ROLE));
            }
        }

        public async Task<bool> RoleExistsAsync(string roleName)
        {
            return await _roleManager.RoleExistsAsync(roleName);
        }

        public async Task<List<string>> GetAvailableRolesAsync()
        {
            var roles = await _roleManager.Roles.Select(r => r.Name).ToListAsync();
            return roles ?? new List<string>();
        }
    }
}
