using stage.Models;

namespace stage.Repositories
{
    public interface INotificationRepository : IBaseRepository<Notification>
    {
        Task<IEnumerable<Notification>> GetByUtilisateurIdAsync(Guid utilisateurId);
        Task<IEnumerable<Notification>> GetUnreadByUtilisateurIdAsync(Guid utilisateurId);
        Task<int> GetUnreadCountAsync(Guid utilisateurId);
        Task<IEnumerable<Notification>> GetBySocieteIdAsync(Guid societeId);
        Task<bool> MarkAsReadAsync(Guid notificationId);
        Task<bool> MarkAllAsReadAsync(Guid utilisateurId);
    }
}
