using stage.Models;

namespace stage.Repositories
{
    public interface IRetenueSourceRepository : IBaseRepository<RetenueSource>
    {
        Task<IEnumerable<RetenueSource>> GetBySocieteIdAsync(Guid societeId);
        Task<IEnumerable<RetenueSource>> GetByUtilisateurIdAsync(Guid utilisateurId);
        Task<IEnumerable<RetenueSource>> GetWithFilesAsync(Guid societeId); // Retenues avec fichiers
    }
}
