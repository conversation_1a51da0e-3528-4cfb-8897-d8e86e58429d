﻿using System.Text.RegularExpressions;

namespace stage.Models
{
    public class Societe
    {
        public Guid Id { get; set; }
        public string Nom { get; set; }
        public string Adresse { get; set; }
        public string MatriculeFiscale { get; set; }
        public string? Email { get; set; }
        public string? Telephone { get; set; }
        public string? Logo { get; set; }
        public string? Signature { get; set; }
        public string? Cachet { get; set; }
        public ICollection<Applicationuser> Utilisateurs { get; set; }
        public ICollection<Client> Clients { get; set; }
        public ICollection<Fournisseur> Fournisseurs { get; set; }
        public ICollection<FactureAchat> FacturesAchat { get; set; }
        public ICollection<FactureVente> FacturesVente { get; set; }
        public ICollection<Document> Documents { get; set; }
        public ICollection<RetenueSource> RetenuesSource { get; set; }

    }
}