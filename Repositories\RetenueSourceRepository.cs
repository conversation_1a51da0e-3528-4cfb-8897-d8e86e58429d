using Microsoft.EntityFrameworkCore;
using stage.Models;

namespace stage.Repositories
{
    public class RetenueSourceRepository : BaseRepository<RetenueSource>, IRetenueSourceRepository
    {
        public RetenueSourceRepository(StageContext context) : base(context)
        {
        }

        public async Task<IEnumerable<RetenueSource>> GetBySocieteIdAsync(Guid societeId)
        {
            return await _dbSet
                .Include(r => r.Utilisateur)
                .Include(r => r.Societe)
                .Where(r => r.SocieteId == societeId)
                .OrderByDescending(r => r.DateScan)
                .ToListAsync();
        }

        public async Task<IEnumerable<RetenueSource>> GetByUtilisateurIdAsync(Guid utilisateurId)
        {
            return await _dbSet
                .Include(r => r.Societe)
                .Where(r => r.UtilisateurId == utilisateurId)
                .OrderByDescending(r => r.DateScan)
                .ToListAsync();
        }

        public async Task<IEnumerable<RetenueSource>> GetWithFilesAsync(Guid societeId)
        {
            return await _dbSet
                .Include(r => r.Utilisateur)
                .Where(r => r.SocieteId == societeId && !string.IsNullOrEmpty(r.CheminFichier))
                .OrderByDescending(r => r.DateScan)
                .ToListAsync();
        }

        public override async Task<RetenueSource?> GetByIdAsync(Guid id)
        {
            return await _dbSet
                .Include(r => r.Societe)
                .Include(r => r.Utilisateur)
                .FirstOrDefaultAsync(r => r.Id == id);
        }
    }
}
