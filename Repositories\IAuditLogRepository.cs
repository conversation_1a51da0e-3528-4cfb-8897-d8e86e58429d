using stage.Models;

namespace stage.Repositories
{
    public interface IAuditLogRepository : IBaseRepository<AuditLog>
    {
        Task<IEnumerable<AuditLog>> GetByUtilisateurIdAsync(Guid utilisateurId);
        Task<IEnumerable<AuditLog>> GetBySocieteIdAsync(Guid societeId);
        Task<IEnumerable<AuditLog>> GetByEntityAsync(TypeEntite typeEntite, Guid entiteId);
        Task<IEnumerable<AuditLog>> GetByActionAsync(TypeAction action);
        Task<IEnumerable<AuditLog>> GetByDateRangeAsync(DateTime dateDebut, DateTime dateFin);
        Task<IEnumerable<AuditLog>> GetRecentActivityAsync(Guid societeId, int count = 50);
    }
}
