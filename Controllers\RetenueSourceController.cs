using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using stage.Models;
using stage.Repositories;
using stage.Services;
using System.Security.Claims;

namespace stage.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class RetenueSourceController : ControllerBase
    {
        private readonly IRetenueSourceRepository _retenueSourceRepository;
        private readonly IUtilisateurRepository _utilisateurRepository;
        private readonly ILogger<RetenueSourceController> _logger;
        private readonly INotificationService _notificationService;
        private readonly IAuditService _auditService;

        public RetenueSourceController(
            IRetenueSourceRepository retenueSourceRepository,
            IUtilisateurRepository utilisateurRepository,
            ILogger<RetenueSourceController> logger,
            INotificationService notificationService,
            IAuditService auditService)
        {
            _retenueSourceRepository = retenueSourceRepository;
            _utilisateurRepository = utilisateurRepository;
            _logger = logger;
            _notificationService = notificationService;
            _auditService = auditService;
        }

        private async Task<Guid?> GetUserSocieteIdAsync()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
                return null;

            var utilisateur = await _utilisateurRepository.GetByIdAsync(userId);
            return utilisateur?.SocieteId;
        }

        private async Task<Guid?> GetUserIdAsync()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
                return null;

            return userId;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<RetenueSource>>> GetAll()
        {
            try
            {
                var societeId = await GetUserSocieteIdAsync();
                if (societeId == null)
                    return Unauthorized("Utilisateur non trouvé ou société non associée.");

                var retenues = await _retenueSourceRepository.GetBySocieteIdAsync(societeId.Value);
                return Ok(retenues);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des retenues à la source");
                return StatusCode(500, "Erreur interne du serveur");
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<RetenueSource>> GetById(Guid id)
        {
            try
            {
                var societeId = await GetUserSocieteIdAsync();
                if (societeId == null)
                    return Unauthorized("Utilisateur non trouvé ou société non associée.");

                var retenue = await _retenueSourceRepository.GetByIdAsync(id);
                if (retenue == null)
                    return NotFound("Retenue à la source non trouvée.");

                if (retenue.SocieteId != societeId.Value)
                    return Forbid("Accès non autorisé à cette retenue.");

                return Ok(retenue);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération de la retenue {Id}", id);
                return StatusCode(500, "Erreur interne du serveur");
            }
        }

        [HttpPost("upload")]
        public async Task<ActionResult<RetenueSource>> CreateFromUpload(IFormFile file, [FromForm] string? commentaires = null)
        {
            try
            {
                var societeId = await GetUserSocieteIdAsync();
                var userId = await GetUserIdAsync();

                if (societeId == null || userId == null)
                    return Unauthorized("Utilisateur non trouvé ou société non associée.");

                if (file == null || file.Length == 0)
                    return BadRequest("Aucun fichier fourni.");

                var allowedExtensions = new[] { ".pdf", ".jpg", ".jpeg", ".png" };
                var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
                if (!allowedExtensions.Contains(fileExtension))
                    return BadRequest("Type de fichier non autorisé. Formats acceptés : PDF, JPG, PNG");

                var fileName = $"{Guid.NewGuid()}{fileExtension}";
                var uploadsPath = Path.Combine("uploads", "retenues");
                Directory.CreateDirectory(uploadsPath);
                var filePath = Path.Combine(uploadsPath, fileName);

                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                var retenue = new RetenueSource
                {
                    SocieteId = societeId.Value,
                    UtilisateurId = userId.Value,
                    DateScan = DateTime.UtcNow,
                    CheminFichier = filePath,
                    Commentaires = commentaires
                };

                await _retenueSourceRepository.AddAsync(retenue);
                await _retenueSourceRepository.SaveChangesAsync();

                // Notification à l'utilisateur qui a uploadé
                await _notificationService.NotifyRetenueSourceAddedAsync(userId.Value, retenue.Id);

                // Notification à tous les admins de la société
                await _notificationService.NotifyAdminsRetenueSourceAddedAsync(societeId.Value, retenue.Id, User.Identity?.Name ?? "Utilisateur");

                return CreatedAtAction(nameof(GetById), new { id = retenue.Id }, retenue);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de l'upload de la retenue à la source");
                return StatusCode(500, "Erreur interne du serveur");
            }
        }

        

        [HttpPut("{id}")]
        public async Task<ActionResult<RetenueSource>> Update(Guid id, [FromBody] RetenueSource retenue)
        {
            try
            {
                var societeId = await GetUserSocieteIdAsync();
                if (societeId == null)
                    return Unauthorized("Utilisateur non trouvé ou société non associée.");

                var existingRetenue = await _retenueSourceRepository.GetByIdAsync(id);
                if (existingRetenue == null)
                    return NotFound("Retenue à la source non trouvée.");

                if (existingRetenue.SocieteId != societeId.Value)
                    return Forbid("Accès non autorisé à cette retenue.");

                existingRetenue.DateScan = retenue.DateScan;
                existingRetenue.CheminFichier = retenue.CheminFichier;
                existingRetenue.Commentaires = retenue.Commentaires;

                _retenueSourceRepository.Update(existingRetenue);
                await _retenueSourceRepository.SaveChangesAsync();
                return Ok(existingRetenue);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la mise à jour de la retenue {Id}", id);
                return StatusCode(500, "Erreur interne du serveur");
            }
        }

        [HttpDelete("{id}")]
        public async Task<ActionResult> Delete(Guid id)
        {
            try
            {
                var societeId = await GetUserSocieteIdAsync();
                if (societeId == null)
                    return Unauthorized("Utilisateur non trouvé ou société non associée.");

                var retenue = await _retenueSourceRepository.GetByIdAsync(id);
                if (retenue == null)
                    return NotFound("Retenue à la source non trouvée.");

                if (retenue.SocieteId != societeId.Value)
                    return Forbid("Accès non autorisé à cette retenue.");

                _retenueSourceRepository.Remove(retenue);
                await _retenueSourceRepository.SaveChangesAsync();
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la suppression de la retenue {Id}", id);
                return StatusCode(500, "Erreur interne du serveur");
            }
        }



    }
}
