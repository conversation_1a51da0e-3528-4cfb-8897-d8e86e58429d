﻿using System.ComponentModel.DataAnnotations;

namespace stage.DTOs.Auth
{
    public class UserRegisterDto
    {
        [Required(ErrorMessage = "Le nom est requis.")]
        [StringLength(100, MinimumLength = 2, ErrorMessage = "Le nom doit avoir entre 2 et 100 caractères.")]
        public string Nom { get; set; }

        [Required(ErrorMessage = "L'adresse email est requise.")]
        [EmailAddress(ErrorMessage = "Format d'adresse email invalide.")]
        [StringLength(256, ErrorMessage = "L'email ne peut pas dépasser 256 caractères.")]
        public string Email { get; set; }

        [Required(ErrorMessage = "Le mot de passe est requis.")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "Le mot de passe doit avoir au moins 6 caractères.")]
        public string MotDePasse { get; set; }

        [Required(ErrorMessage = "Le nom de la société est requis.")]
        [StringLength(200, MinimumLength = 2, ErrorMessage = "Le nom de la société doit avoir entre 2 et 200 caractères.")]
        public string SocieteNom { get; set; }

        [Required(ErrorMessage = "L'adresse de la société est requise.")]
        [StringLength(500, MinimumLength = 5, ErrorMessage = "L'adresse de la société doit avoir entre 5 et 500 caractères.")]
        public string SocieteAdresse { get; set; }

        [Required(ErrorMessage = "Le matricule fiscal est requis.")]
        [StringLength(50, MinimumLength = 5, ErrorMessage = "Le matricule fiscal doit avoir entre 5 et 50 caractères.")]
        public string MatriculeFiscale { get; set; }

        [StringLength(500, ErrorMessage = "La signature ne peut pas dépasser 500 caractères.")]
        public string? Signature { get; set; }

        [StringLength(500, ErrorMessage = "Le cachet ne peut pas dépasser 500 caractères.")]
        public string? Cachet { get; set; }
    }
}