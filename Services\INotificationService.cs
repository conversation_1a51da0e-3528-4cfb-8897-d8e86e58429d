using stage.Models;

namespace stage.Services
{
    public interface INotificationService
    {
        Task<Notification> CreateNotificationAsync(Guid utilisateurId, TypeNotification type, string titre, string message, string? lienAction = null, Dictionary<string, object>? metadata = null);
        Task<List<Notification>> GetUserNotificationsAsync(Guid utilisateurId, bool includeRead = true);
        Task<List<Notification>> GetUnreadNotificationsAsync(Guid utilisateurId);
        Task<bool> MarkAsReadAsync(Guid notificationId, Guid utilisateurId);
        Task<bool> MarkAllAsReadAsync(Guid utilisateurId);
        Task<bool> DeleteNotificationAsync(Guid notificationId, Guid utilisateurId);
        Task<int> GetUnreadCountAsync(Guid utilisateurId);
        Task NotifyFactureCreatedAsync(Guid utilisateurId, Guid factureId, string numeroFacture);
        Task NotifyFactureStatusChangedAsync(Guid utilisateurId, Guid factureId, string numeroFacture, StatutFacture nouveauStatut);
        Task NotifyDocumentStatusChangedAsync(Guid utilisateurId, Guid documentId, string titreDocument, StatutDocument nouveauStatut);
        Task NotifyAdminDocumentRequestAsync(Guid societeId, Guid documentId, string titreDocument, string demandeurNom, bool estUrgent = false);
        Task NotifyUserCreatedAsync(Guid adminId, Guid newUserId, string newUserName);
        Task NotifyRetenueSourceAddedAsync(Guid utilisateurId, Guid retenueId);
        Task SendSystemNotificationAsync(Guid societeId, string titre, string message);
    }
}
