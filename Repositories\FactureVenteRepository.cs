using Microsoft.EntityFrameworkCore;
using stage.Models;

namespace stage.Repositories
{
    public class FactureVenteRepository : BaseRepository<FactureVente>, IFactureVenteRepository
    {
        public FactureVenteRepository(StageContext context) : base(context)
        {
        }

        public async Task<IEnumerable<FactureVente>> GetFacturesVenteBySocieteIdAsync(Guid societeId)
        {
            return await _dbSet
                .Include(f => f.Client)
                .Include(f => f.Societe)
                .Where(f => f.SocieteId == societeId)
                .ToListAsync();
        }

        public async Task<IEnumerable<FactureVente>> GetFacturesVenteByClientIdAsync(Guid clientId, Guid societeId)
        {
            return await _dbSet
                .Include(f => f.Client)
                .Include(f => f.Societe)
                .Where(f => f.ClientId == clientId && f.SocieteId == societeId)
                .ToListAsync();
        }

        public override async Task<FactureVente?> GetByIdAsync(Guid id)
        {
            return await _dbSet
                .Include(f => f.Client)
                .Include(f => f.Societe)
                .FirstOrDefaultAsync(f => f.Id == id);
        }
    }
}
