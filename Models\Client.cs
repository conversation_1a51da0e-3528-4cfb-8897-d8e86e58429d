namespace stage.Models
{
    public enum StatutClient
    {
        Actif = 0,
        Inactif = 1,
        Suspendu = 2
    }

    public class Client
    {
        public Guid Id { get; set; }
        public string Nom { get; set; }
        public string Email { get; set; }
        public string? Telephone { get; set; }
        public string? TelephoneMobile { get; set; }
        public string Adresse { get; set; }
        public StatutClient Statut { get; set; } = StatutClient.Actif;
        public DateTime DateCreation { get; set; } = DateTime.UtcNow;
        public DateTime? DateModification { get; set; }
        public string? NotesInternes { get; set; }
        public decimal ChiffreAffaireTotal { get; set; } = 0;
        public int NombreFactures { get; set; } = 0;
        public DateTime? DernierePaiement { get; set; }

        public Guid SocieteId { get; set; }
        public Societe Societe { get; set; }
        public ICollection<FactureVente>? FacturesVente { get; set; }
    }
}