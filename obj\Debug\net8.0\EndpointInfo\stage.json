{"openapi": "3.0.1", "info": {"title": "stage", "version": "1.0"}, "paths": {"/api/Auth/register": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserRegisterDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserRegisterDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserRegisterDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserLoginDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserLoginDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserLoginDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Auth/logout": {"post": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/Clients": {"get": {"tags": ["Clients"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Client"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Client"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Client"}}}}}}}, "post": {"tags": ["Clients"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Client"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Client"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Client"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Client"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Client"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Client"}}}}}}}, "/api/Clients/{id}": {"get": {"tags": ["Clients"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Client"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Client"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Client"}}}}}}, "put": {"tags": ["Clients"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Client"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Client"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Client"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Clients"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Documents": {"get": {"tags": ["Documents"], "parameters": [{"name": "search", "in": "query", "schema": {"type": "string"}}, {"name": "statut", "in": "query", "schema": {"$ref": "#/components/schemas/StatutDocument"}}, {"name": "typeDemande", "in": "query", "schema": {"$ref": "#/components/schemas/TypeDemandeDocument"}}, {"name": "typeDocumentId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "estUrgent", "in": "query", "schema": {"type": "boolean"}}, {"name": "dateDebut", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "dateFin", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Document"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Document"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Document"}}}}}}}, "post": {"tags": ["Documents"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Document"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Document"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Document"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Document"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Document"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Document"}}}}}}}, "/api/Documents/{id}": {"get": {"tags": ["Documents"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Document"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Document"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Document"}}}}}}, "put": {"tags": ["Documents"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Document"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Document"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Document"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Documents"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Documents/{id}/approve": {"post": {"tags": ["Documents"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}, "application/*+json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Documents/{id}/reject": {"post": {"tags": ["Documents"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}, "application/*+json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/factures-achat": {"get": {"tags": ["FacturesAchat"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FactureAchat"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FactureAchat"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FactureAchat"}}}}}}}, "post": {"tags": ["FacturesAchat"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FactureAchat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FactureAchat"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FactureAchat"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FactureAchat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FactureAchat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FactureAchat"}}}}}}}, "/api/factures-achat/{id}": {"get": {"tags": ["FacturesAchat"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FactureAchat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FactureAchat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FactureAchat"}}}}}}, "put": {"tags": ["FacturesAchat"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FactureAchat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FactureAchat"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FactureAchat"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["FacturesAchat"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/factures-vente": {"get": {"tags": ["FacturesVente"], "parameters": [{"name": "search", "in": "query", "schema": {"type": "string"}}, {"name": "statut", "in": "query", "schema": {"$ref": "#/components/schemas/StatutFacture"}}, {"name": "clientId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "dateDebut", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "dateFin", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "montantMin", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "montantMax", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FactureVente"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FactureVente"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FactureVente"}}}}}}}, "post": {"tags": ["FacturesVente"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FactureVente"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FactureVente"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FactureVente"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FactureVente"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FactureVente"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FactureVente"}}}}}}}, "/api/factures-vente/{id}": {"get": {"tags": ["FacturesVente"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FactureVente"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FactureVente"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FactureVente"}}}}}}, "put": {"tags": ["FacturesVente"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FactureVente"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FactureVente"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FactureVente"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["FacturesVente"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/factures-vente/{id}/change-status": {"post": {"tags": ["FacturesVente"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StatutFacture"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StatutFacture"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StatutFacture"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Files/retenue/{id}": {"get": {"tags": ["Files"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Files/upload": {"post": {"tags": ["Files"], "parameters": [{"name": "subfolder", "in": "query", "schema": {"type": "string", "default": "uploads"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Files/info": {"get": {"tags": ["Files"], "parameters": [{"name": "filePath", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Fournisseurs": {"get": {"tags": ["Fournisseurs"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Fournisseur"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Fournisseur"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Fournisseur"}}}}}}}, "post": {"tags": ["Fournisseurs"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Fournisseur"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Fournisseur"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Fournisseur"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Fournisseur"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Fournisseur"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Fournisseur"}}}}}}}, "/api/Fournisseurs/{id}": {"get": {"tags": ["Fournisseurs"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Fournisseur"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Fournisseur"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Fournisseur"}}}}}}, "put": {"tags": ["Fournisseurs"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Fournisseur"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Fournisseur"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Fournisseur"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Fournisseurs"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Notifications": {"get": {"tags": ["Notifications"], "parameters": [{"name": "includeRead", "in": "query", "schema": {"type": "boolean", "default": true}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Notification"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Notification"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Notification"}}}}}}}}, "/api/Notifications/unread": {"get": {"tags": ["Notifications"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Notification"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Notification"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Notification"}}}}}}}}, "/api/Notifications/unread-count": {"get": {"tags": ["Notifications"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "integer", "format": "int32"}}, "application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/api/Notifications/{id}/mark-read": {"post": {"tags": ["Notifications"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Notifications/mark-all-read": {"post": {"tags": ["Notifications"], "responses": {"200": {"description": "OK"}}}}, "/api/Notifications/{id}": {"delete": {"tags": ["Notifications"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/RetenueSource": {"get": {"tags": ["RetenueSource"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RetenueSource"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RetenueSource"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RetenueSource"}}}}}}}}, "/api/RetenueSource/{id}": {"get": {"tags": ["RetenueSource"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RetenueSource"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RetenueSource"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RetenueSource"}}}}}}, "put": {"tags": ["RetenueSource"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RetenueSource"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RetenueSource"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RetenueSource"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RetenueSource"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RetenueSource"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RetenueSource"}}}}}}, "delete": {"tags": ["RetenueSource"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/RetenueSource/upload": {"post": {"tags": ["RetenueSource"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}, "commentaires": {"type": "string"}}}, "encoding": {"file": {"style": "form"}, "commentaires": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RetenueSource"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RetenueSource"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RetenueSource"}}}}}}}, "/api/RetenueSource/scan": {"post": {"tags": ["RetenueSource"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}, "commentaires": {"type": "string"}}}, "encoding": {"file": {"style": "form"}, "commentaires": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Societes": {"get": {"tags": ["Societes"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Societe"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Societe"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Societe"}}}}}}, "put": {"tags": ["Societes"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Societe"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Societe"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Societe"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Utilisateurs": {"get": {"tags": ["Utilisateurs"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UtilisateurDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UtilisateurDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UtilisateurDto"}}}}}}}, "post": {"tags": ["Utilisateurs"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UtilisateurCreateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UtilisateurCreateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UtilisateurCreateDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UtilisateurDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UtilisateurDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UtilisateurDto"}}}}}}}, "/api/Utilisateurs/{id}": {"get": {"tags": ["Utilisateurs"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UtilisateurDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UtilisateurDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UtilisateurDto"}}}}}}, "put": {"tags": ["Utilisateurs"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UtilisateurUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UtilisateurUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UtilisateurUpdateDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Utilisateurs"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/WeatherForecast": {"get": {"tags": ["WeatherForecast"], "operationId": "GetWeatherForecast", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}}}}}}}, "components": {"schemas": {"Applicationuser": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "userName": {"type": "string", "nullable": true}, "normalizedUserName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "normalizedEmail": {"type": "string", "nullable": true}, "emailConfirmed": {"type": "boolean"}, "passwordHash": {"type": "string", "nullable": true}, "securityStamp": {"type": "string", "nullable": true}, "concurrencyStamp": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "phoneNumberConfirmed": {"type": "boolean"}, "twoFactorEnabled": {"type": "boolean"}, "lockoutEnd": {"type": "string", "format": "date-time", "nullable": true}, "lockoutEnabled": {"type": "boolean"}, "accessFailedCount": {"type": "integer", "format": "int32"}, "nom": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "societeId": {"type": "string", "format": "uuid"}, "societe": {"$ref": "#/components/schemas/Societe"}}, "additionalProperties": false}, "CategorieFournisseur": {"enum": [0, 1, 2, 3, 4], "type": "integer", "format": "int32"}, "Client": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "nom": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "telephone": {"type": "string", "nullable": true}, "telephoneMobile": {"type": "string", "nullable": true}, "adresse": {"type": "string", "nullable": true}, "statut": {"$ref": "#/components/schemas/StatutClient"}, "dateCreation": {"type": "string", "format": "date-time"}, "dateModification": {"type": "string", "format": "date-time", "nullable": true}, "notesInternes": {"type": "string", "nullable": true}, "chiffreAffaireTotal": {"type": "number", "format": "double"}, "nombreFactures": {"type": "integer", "format": "int32"}, "dernierePaiement": {"type": "string", "format": "date-time", "nullable": true}, "societeId": {"type": "string", "format": "uuid"}, "societe": {"$ref": "#/components/schemas/Societe"}, "facturesVente": {"type": "array", "items": {"$ref": "#/components/schemas/FactureVente"}, "nullable": true}}, "additionalProperties": false}, "Document": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "titre": {"type": "string", "nullable": true}, "contenu": {"type": "string", "nullable": true}, "dateCreation": {"type": "string", "format": "date-time"}, "dateModification": {"type": "string", "format": "date-time", "nullable": true}, "statut": {"$ref": "#/components/schemas/StatutDocument"}, "typeDemande": {"$ref": "#/components/schemas/TypeDemandeDocument"}, "estUrgent": {"type": "boolean"}, "emailCopie": {"type": "string", "nullable": true}, "commentairesApprobation": {"type": "string", "nullable": true}, "typeDocumentId": {"type": "string", "format": "uuid"}, "typeDocument": {"$ref": "#/components/schemas/TypeDocument"}, "societeId": {"type": "string", "format": "uuid"}, "societe": {"$ref": "#/components/schemas/Societe"}, "utilisateurDemandeurId": {"type": "string", "format": "uuid", "nullable": true}, "utilisateurDemandeur": {"$ref": "#/components/schemas/Applicationuser"}, "utilisateurApprobateurId": {"type": "string", "format": "uuid", "nullable": true}, "utilisateurApprobateur": {"$ref": "#/components/schemas/Applicationuser"}, "dateApprobation": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "FactureAchat": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "numero": {"type": "string", "nullable": true}, "montant": {"type": "number", "format": "double"}, "date": {"type": "string", "format": "date-time"}, "dateEcheance": {"type": "string", "format": "date-time", "nullable": true}, "statut": {"$ref": "#/components/schemas/StatutFacture"}, "dateEnvoi": {"type": "string", "format": "date-time", "nullable": true}, "datePaiement": {"type": "string", "format": "date-time", "nullable": true}, "notesInternes": {"type": "string", "nullable": true}, "matriculeFiscaleSociete": {"type": "string", "nullable": true}, "societeId": {"type": "string", "format": "uuid"}, "societe": {"$ref": "#/components/schemas/Societe"}, "utilisateurCreationId": {"type": "string", "format": "uuid", "nullable": true}, "utilisateurCreation": {"$ref": "#/components/schemas/Applicationuser"}, "dateCreation": {"type": "string", "format": "date-time"}, "dateModification": {"type": "string", "format": "date-time", "nullable": true}, "fournisseurId": {"type": "string", "format": "uuid"}, "fournisseur": {"$ref": "#/components/schemas/Fournisseur"}}, "additionalProperties": false}, "FactureVente": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "numero": {"type": "string", "nullable": true}, "montant": {"type": "number", "format": "double"}, "date": {"type": "string", "format": "date-time"}, "dateEcheance": {"type": "string", "format": "date-time", "nullable": true}, "statut": {"$ref": "#/components/schemas/StatutFacture"}, "dateEnvoi": {"type": "string", "format": "date-time", "nullable": true}, "datePaiement": {"type": "string", "format": "date-time", "nullable": true}, "notesInternes": {"type": "string", "nullable": true}, "matriculeFiscaleSociete": {"type": "string", "nullable": true}, "societeId": {"type": "string", "format": "uuid"}, "societe": {"$ref": "#/components/schemas/Societe"}, "utilisateurCreationId": {"type": "string", "format": "uuid", "nullable": true}, "utilisateurCreation": {"$ref": "#/components/schemas/Applicationuser"}, "dateCreation": {"type": "string", "format": "date-time"}, "dateModification": {"type": "string", "format": "date-time", "nullable": true}, "clientId": {"type": "string", "format": "uuid"}, "client": {"$ref": "#/components/schemas/Client"}}, "additionalProperties": false}, "Fournisseur": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "nom": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "telephone": {"type": "string", "nullable": true}, "telephoneMobile": {"type": "string", "nullable": true}, "adresse": {"type": "string", "nullable": true}, "statut": {"$ref": "#/components/schemas/StatutFournisseur"}, "categorie": {"$ref": "#/components/schemas/CategorieFournisseur"}, "dateCreation": {"type": "string", "format": "date-time"}, "dateModification": {"type": "string", "format": "date-time", "nullable": true}, "notesInternes": {"type": "string", "nullable": true}, "montantTotalCommandes": {"type": "number", "format": "double"}, "nombreCommandes": {"type": "integer", "format": "int32"}, "derniereCommande": {"type": "string", "format": "date-time", "nullable": true}, "societeId": {"type": "string", "format": "uuid"}, "societe": {"$ref": "#/components/schemas/Societe"}, "facturesAchat": {"type": "array", "items": {"$ref": "#/components/schemas/FactureAchat"}, "nullable": true}}, "additionalProperties": false}, "Notification": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "titre": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "type": {"$ref": "#/components/schemas/TypeNotification"}, "statut": {"$ref": "#/components/schemas/StatutNotification"}, "dateCreation": {"type": "string", "format": "date-time"}, "dateLecture": {"type": "string", "format": "date-time", "nullable": true}, "lienAction": {"type": "string", "nullable": true}, "donneesMetadata": {"type": "string", "nullable": true}, "utilisateurId": {"type": "string", "format": "uuid"}, "utilisateur": {"$ref": "#/components/schemas/Applicationuser"}, "societeId": {"type": "string", "format": "uuid"}, "societe": {"$ref": "#/components/schemas/Societe"}, "factureId": {"type": "string", "format": "uuid", "nullable": true}, "documentId": {"type": "string", "format": "uuid", "nullable": true}, "clientId": {"type": "string", "format": "uuid", "nullable": true}, "fournisseurId": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "RetenueSource": {"required": ["societeId", "utilisateurId"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "dateScan": {"type": "string", "format": "date-time", "nullable": true}, "cheminFichier": {"maxLength": 500, "type": "string", "nullable": true}, "societeId": {"type": "string", "format": "uuid"}, "societe": {"$ref": "#/components/schemas/Societe"}, "utilisateurId": {"type": "string", "format": "uuid"}, "utilisateur": {"$ref": "#/components/schemas/Applicationuser"}, "commentaires": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Societe": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "nom": {"type": "string", "nullable": true}, "adresse": {"type": "string", "nullable": true}, "matriculeFiscale": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "telephone": {"type": "string", "nullable": true}, "logo": {"type": "string", "nullable": true}, "signature": {"type": "string", "nullable": true}, "cachet": {"type": "string", "nullable": true}, "utilisateurs": {"type": "array", "items": {"$ref": "#/components/schemas/Applicationuser"}, "nullable": true}, "clients": {"type": "array", "items": {"$ref": "#/components/schemas/Client"}, "nullable": true}, "fournisseurs": {"type": "array", "items": {"$ref": "#/components/schemas/Fournisseur"}, "nullable": true}, "facturesAchat": {"type": "array", "items": {"$ref": "#/components/schemas/FactureAchat"}, "nullable": true}, "facturesVente": {"type": "array", "items": {"$ref": "#/components/schemas/FactureVente"}, "nullable": true}, "documents": {"type": "array", "items": {"$ref": "#/components/schemas/Document"}, "nullable": true}, "retenuesSource": {"type": "array", "items": {"$ref": "#/components/schemas/RetenueSource"}, "nullable": true}}, "additionalProperties": false}, "StatutClient": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "StatutDocument": {"enum": [0, 1, 2, 3, 4], "type": "integer", "format": "int32"}, "StatutFacture": {"enum": [0, 1, 2, 3, 4], "type": "integer", "format": "int32"}, "StatutFournisseur": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "StatutNotification": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "TypeDemandeDocument": {"enum": [0, 1, 2, 3], "type": "integer", "format": "int32"}, "TypeDocument": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "nom": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "societeId": {"type": "string", "format": "uuid"}, "societe": {"$ref": "#/components/schemas/Societe"}, "documents": {"type": "array", "items": {"$ref": "#/components/schemas/Document"}, "nullable": true}}, "additionalProperties": false}, "TypeNotification": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9], "type": "integer", "format": "int32"}, "UserLoginDto": {"required": ["email", "motDePasse"], "type": "object", "properties": {"email": {"minLength": 1, "type": "string", "format": "email"}, "motDePasse": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "UserRegisterDto": {"required": ["email", "matriculeFiscale", "motDePasse", "nom", "societeAdresse", "societeNom"], "type": "object", "properties": {"nom": {"maxLength": 100, "minLength": 2, "type": "string"}, "email": {"maxLength": 256, "minLength": 0, "type": "string", "format": "email"}, "motDePasse": {"maxLength": 100, "minLength": 6, "type": "string"}, "societeNom": {"maxLength": 200, "minLength": 2, "type": "string"}, "societeAdresse": {"maxLength": 500, "minLength": 5, "type": "string"}, "matriculeFiscale": {"maxLength": 50, "minLength": 5, "type": "string"}, "signature": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "cachet": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}}, "additionalProperties": false}, "UtilisateurCreateDto": {"required": ["email", "motDePasse", "nom", "role"], "type": "object", "properties": {"nom": {"maxLength": 100, "minLength": 2, "type": "string"}, "email": {"maxLength": 256, "minLength": 0, "type": "string", "format": "email"}, "motDePasse": {"maxLength": 100, "minLength": 6, "type": "string"}, "role": {"maxLength": 50, "minLength": 0, "type": "string"}}, "additionalProperties": false}, "UtilisateurDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "nom": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "role": {"type": "string", "nullable": true}, "societeId": {"type": "string", "format": "uuid"}, "societeNom": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UtilisateurUpdateDto": {"required": ["email", "id", "nom", "role"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "nom": {"maxLength": 100, "minLength": 2, "type": "string"}, "email": {"maxLength": 256, "minLength": 0, "type": "string", "format": "email"}, "motDePasse": {"maxLength": 100, "minLength": 6, "type": "string", "nullable": true}, "role": {"maxLength": 50, "minLength": 0, "type": "string"}}, "additionalProperties": false}, "WeatherForecast": {"type": "object", "properties": {"date": {"type": "string", "format": "date"}, "temperatureC": {"type": "integer", "format": "int32"}, "temperatureF": {"type": "integer", "format": "int32", "readOnly": true}, "summary": {"type": "string", "nullable": true}}, "additionalProperties": false}}}}