using Microsoft.EntityFrameworkCore;
using stage.Models;

namespace stage.Repositories
{
    public class DocumentRepository : BaseRepository<Document>, IDocumentRepository
    {
        public DocumentRepository(StageContext context) : base(context)
        {
        }

        public async Task<IEnumerable<Document>> GetDocumentsBySocieteIdAsync(Guid societeId)
        {
            return await _dbSet
                .Include(d => d.TypeDocument)
                .Include(d => d.Societe)
                .Where(d => d.SocieteId == societeId)
                .ToListAsync();
        }

        public async Task<IEnumerable<Document>> GetDocumentsByTypeAsync(Guid typeDocumentId, Guid societeId)
        {
            return await _dbSet
                .Include(d => d.TypeDocument)
                .Include(d => d.Societe)
                .Where(d => d.TypeDocumentId == typeDocumentId && d.SocieteId == societeId)
                .ToListAsync();
        }

        public override async Task<Document?> GetByIdAsync(Guid id)
        {
            return await _dbSet
                .Include(d => d.TypeDocument)
                .Include(d => d.Societe)
                .FirstOrDefaultAsync(d => d.Id == id);
        }
    }
}
