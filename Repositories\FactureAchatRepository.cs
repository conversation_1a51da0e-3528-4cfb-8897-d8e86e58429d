using Microsoft.EntityFrameworkCore;
using stage.Models;

namespace stage.Repositories
{
    public class FactureAchatRepository : BaseRepository<FactureAchat>, IFactureAchatRepository
    {
        public FactureAchatRepository(StageContext context) : base(context)
        {
        }

        public async Task<IEnumerable<FactureAchat>> GetFacturesAchatBySocieteIdAsync(Guid societeId)
        {
            return await _dbSet
                .Include(f => f.Fournisseur)
                .Include(f => f.Societe)
                .Where(f => f.SocieteId == societeId)
                .ToListAsync();
        }

        public async Task<IEnumerable<FactureAchat>> GetFacturesAchatByFournisseurIdAsync(Guid fournisseurId, Guid societeId)
        {
            return await _dbSet
                .Include(f => f.Fournisseur)
                .Include(f => f.Societe)
                .Where(f => f.FournisseurId == fournisseurId && f.SocieteId == societeId)
                .ToListAsync();
        }

        public override async Task<FactureAchat?> GetByIdAsync(Guid id)
        {
            return await _dbSet
                .Include(f => f.Fournisseur)
                .Include(f => f.Societe)
                .FirstOrDefaultAsync(f => f.Id == id);
        }
    }
}
