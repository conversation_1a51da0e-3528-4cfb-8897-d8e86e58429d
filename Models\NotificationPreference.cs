namespace stage.Models
{
    public enum CanalNotification
    {
        Email = 0,
        Push = 1,
        SMS = 2
    }

    public class NotificationPreference
    {
        public Guid Id { get; set; }
        public TypeNotification TypeNotification { get; set; }
        public CanalNotification Canal { get; set; }
        public bool EstActive { get; set; } = true;

        // Relations
        public Guid UtilisateurId { get; set; }
        public Applicationuser Utilisateur { get; set; }
    }
}
