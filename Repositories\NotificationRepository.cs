using Microsoft.EntityFrameworkCore;
using stage.Models;

namespace stage.Repositories
{
    public class NotificationRepository : BaseRepository<Notification>, INotificationRepository
    {
        public NotificationRepository(StageContext context) : base(context)
        {
        }

        public async Task<IEnumerable<Notification>> GetByUtilisateurIdAsync(Guid utilisateurId)
        {
            return await _dbSet
                .Where(n => n.UtilisateurId == utilisateurId)
                .OrderByDescending(n => n.DateCreation)
                .ToListAsync();
        }

        public async Task<IEnumerable<Notification>> GetUnreadByUtilisateurIdAsync(Guid utilisateurId)
        {
            return await _dbSet
                .Where(n => n.UtilisateurId == utilisateurId && n.Statut == StatutNotification.NonLue)
                .OrderByDescending(n => n.DateCreation)
                .ToListAsync();
        }

        public async Task<int> GetUnreadCountAsync(Guid utilisateurId)
        {
            return await _dbSet
                .CountAsync(n => n.UtilisateurId == utilisateurId && n.Statut == StatutNotification.NonLue);
        }

        public async Task<IEnumerable<Notification>> GetBySocieteIdAsync(Guid societeId)
        {
            return await _dbSet
                .Where(n => n.SocieteId == societeId)
                .OrderByDescending(n => n.DateCreation)
                .ToListAsync();
        }

        public async Task<bool> MarkAsReadAsync(Guid notificationId)
        {
            var notification = await _dbSet.FindAsync(notificationId);
            if (notification == null)
                return false;

            notification.Statut = StatutNotification.Lue;
            notification.DateLecture = DateTime.UtcNow;
            
            _context.Entry(notification).State = EntityState.Modified;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> MarkAllAsReadAsync(Guid utilisateurId)
        {
            var notifications = await _dbSet
                .Where(n => n.UtilisateurId == utilisateurId && n.Statut == StatutNotification.NonLue)
                .ToListAsync();

            foreach (var notification in notifications)
            {
                notification.Statut = StatutNotification.Lue;
                notification.DateLecture = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();
            return true;
        }
    }
}
