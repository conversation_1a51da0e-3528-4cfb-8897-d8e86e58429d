using Microsoft.EntityFrameworkCore;
using stage.Models;

namespace stage.Repositories
{
    public class FournisseurRepository : BaseRepository<Fournisseur>, IFournisseurRepository
    {
        public FournisseurRepository(StageContext context) : base(context)
        {
        }

        public async Task<IEnumerable<Fournisseur>> GetFournisseursBySocieteIdAsync(Guid societeId)
        {
            return await _dbSet
                .Include(f => f.Societe)
                .Where(f => f.SocieteId == societeId)
                .ToListAsync();
        }

        public async Task<Fournisseur?> GetFournisseurByEmailAsync(string email, Guid societeId)
        {
            return await _dbSet
                .Include(f => f.Societe)
                .FirstOrDefaultAsync(f => f.Email == email && f.SocieteId == societeId);
        }

        public override async Task<Fournisseur?> GetByIdAsync(Guid id)
        {
            return await _dbSet
                .Include(f => f.Societe)
                .FirstOrDefaultAsync(f => f.Id == id);
        }
    }
}
