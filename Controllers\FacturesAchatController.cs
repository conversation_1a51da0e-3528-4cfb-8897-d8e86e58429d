using Microsoft.AspNetCore.Mvc;
using stage.Models;
using stage.Repositories;
using stage.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;

namespace stage.Controllers
{
    [Route("api/factures-achat")]
    [ApiController]
    [Authorize] 
    public class FacturesAchatController : ControllerBase
    {
        private readonly IFactureAchatRepository _factureAchatRepository;
        private readonly IFournisseurRepository _fournisseurRepository;
        private readonly ISocieteRepository _societeRepository;
        private readonly IAuditService _auditService;

        public FacturesAchatController(
            IFactureAchatRepository factureAchatRepository,
            IFournisseurRepository fournisseurRepository,
            ISocieteRepository societeRepository,
            IAuditService auditService)
        {
            _factureAchatRepository = factureAchatRepository;
            _fournisseurRepository = fournisseurRepository;
            _societeRepository = societeRepository;
            _auditService = auditService;
        }

        private Guid GetUserSocieteId()
        {
            var societeIdClaim = User.FindFirst("SocieteId");
            if (societeIdClaim == null || !Guid.TryParse(societeIdClaim.Value, out var societeId))
            {
                throw new InvalidOperationException("SocieteId claim missing or invalid in JWT.");
            }
            return societeId;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<FactureAchat>>> GetFacturesAchat()
        {
            var societeId = GetUserSocieteId();
            var factures = await _factureAchatRepository.GetFacturesAchatBySocieteIdAsync(societeId);
            return Ok(factures);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<FactureAchat>> GetFactureAchat(Guid id)
        {
            var societeId = GetUserSocieteId();
            var facture = await _factureAchatRepository.GetByIdAsync(id);

            if (facture == null || facture.SocieteId != societeId)
            {
                return NotFound();
            }

            return Ok(facture);
        }

        [HttpPost]
        public async Task<ActionResult<FactureAchat>> PostFactureAchat([FromForm] FactureAchat facture, [FromForm] IFormFile? fichier = null)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var societeId = GetUserSocieteId();
            var userId = GetUserId();

            var fournisseur = await _fournisseurRepository.GetByIdAsync(facture.FournisseurId);
            if (fournisseur == null || fournisseur.SocieteId != societeId)
            {
                return BadRequest("Le fournisseur spécifié n'existe pas ou n'appartient pas à votre société.");
            }

            var societe = await _societeRepository.GetByIdAsync(societeId);
            if (societe == null)
            {
                return BadRequest("Société non trouvée.");
            }

            facture.Id = Guid.NewGuid();
            facture.SocieteId = societeId;
            facture.UtilisateurCreationId = userId;
            facture.Date = DateTime.UtcNow;
            facture.MatriculeFiscaleSociete = societe.MatriculeFiscale;

            // Gestion de l'upload de fichier
            if (fichier != null && fichier.Length > 0)
            {
                // Validation du fichier
                var allowedExtensions = new[] { ".pdf", ".jpg", ".jpeg", ".png", ".doc", ".docx" };
                var fileExtension = Path.GetExtension(fichier.FileName).ToLowerInvariant();

                if (!allowedExtensions.Contains(fileExtension))
                {
                    return BadRequest("Type de fichier non autorisé. Formats acceptés: PDF, JPG, PNG, DOC, DOCX");
                }

                if (fichier.Length > 10 * 1024 * 1024) // 10MB max
                {
                    return BadRequest("Le fichier est trop volumineux (max 10MB).");
                }

                // Sauvegarde du fichier
                var fileName = $"{facture.Id}_{Guid.NewGuid()}{fileExtension}";
                var uploadsFolder = Path.Combine(Directory.GetCurrentDirectory(), "uploads", "factures-achat");
                Directory.CreateDirectory(uploadsFolder);
                var filePath = Path.Combine(uploadsFolder, fileName);

                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await fichier.CopyToAsync(stream);
                }

                // Mise à jour des propriétés de fichier
                facture.CheminFichier = filePath;
                facture.NomFichierOriginal = fichier.FileName;
                facture.TailleFichier = fichier.Length;
                facture.TypeFichier = fichier.ContentType;
                facture.DateUpload = DateTime.UtcNow;
            }

            await _factureAchatRepository.AddAsync(facture);
            await _factureAchatRepository.SaveChangesAsync();

            // Audit (Admin uniquement)
            if (User.IsInRole("Admin"))
            {
                await _auditService.LogActionAsync(
                    TypeAction.Creation,
                    TypeEntite.FactureAchat,
                    facture.Id,
                    facture.Numero,
                    userId,
                    societeId,
                    "Création facture d'achat" + (fichier != null ? " avec fichier joint" : ""),
                    null,
                    facture,
                    GetUserIP(),
                    GetUserAgent()
                );
            }

            var createdFacture = await _factureAchatRepository.GetByIdAsync(facture.Id);
            return CreatedAtAction(nameof(GetFactureAchat), new { id = createdFacture.Id }, createdFacture);
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> PutFactureAchat(Guid id, [FromBody] FactureAchat facture)
        {
            if (id != facture.Id || !ModelState.IsValid)
            {
                return BadRequest();
            }

            var societeId = GetUserSocieteId();
            var existingFacture = await _factureAchatRepository.GetByIdAsync(id);

            if (existingFacture == null || existingFacture.SocieteId != societeId)
            {
                return NotFound();
            }

            var fournisseur = await _fournisseurRepository.GetByIdAsync(facture.FournisseurId);
            if (fournisseur == null || fournisseur.SocieteId != societeId)
            {
                return BadRequest("Le fournisseur spécifié n'existe pas ou n'appartient pas à votre société.");
            }

            existingFacture.Numero = facture.Numero;
            existingFacture.Montant = facture.Montant;
            existingFacture.FournisseurId = facture.FournisseurId;

            _factureAchatRepository.Update(existingFacture);

            try
            {
                await _factureAchatRepository.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!await FactureAchatExists(id, societeId))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        [HttpGet("{id}/download-file")]
        public async Task<IActionResult> DownloadFactureFile(Guid id)
        {
            var societeId = GetUserSocieteId();
            var userId = GetUserId();

            var facture = await _factureAchatRepository.GetByIdAsync(id);
            if (facture == null || facture.SocieteId != societeId)
            {
                return NotFound();
            }

            if (string.IsNullOrEmpty(facture.CheminFichier) || !System.IO.File.Exists(facture.CheminFichier))
            {
                return NotFound("Aucun fichier joint à cette facture.");
            }

            // Audit (Admin uniquement)
            if (User.IsInRole("Admin"))
            {
                await _auditService.LogActionAsync(
                    TypeAction.Download,
                    TypeEntite.FactureAchat,
                    facture.Id,
                    facture.Numero,
                    userId,
                    societeId,
                    "Téléchargement fichier facture d'achat",
                    null,
                    null,
                    GetUserIP(),
                    GetUserAgent()
                );
            }

            var fileBytes = await System.IO.File.ReadAllBytesAsync(facture.CheminFichier);
            var fileName = facture.NomFichierOriginal ?? $"facture_{facture.Numero}.pdf";
            var contentType = facture.TypeFichier ?? "application/octet-stream";

            return File(fileBytes, contentType, fileName);
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteFactureAchat(Guid id)
        {
            var societeId = GetUserSocieteId();
            var userId = GetUserId();
            var facture = await _factureAchatRepository.GetByIdAsync(id);

            if (facture == null || facture.SocieteId != societeId)
            {
                return NotFound();
            }

            // Supprimer le fichier joint s'il existe
            if (!string.IsNullOrEmpty(facture.CheminFichier) && System.IO.File.Exists(facture.CheminFichier))
            {
                System.IO.File.Delete(facture.CheminFichier);
            }

            // Audit (Admin uniquement)
            if (User.IsInRole("Admin"))
            {
                await _auditService.LogActionAsync(
                    TypeAction.Suppression,
                    TypeEntite.FactureAchat,
                    facture.Id,
                    facture.Numero,
                    userId,
                    societeId,
                    "Suppression facture d'achat",
                    facture,
                    null,
                    GetUserIP(),
                    GetUserAgent()
                );
            }

            _factureAchatRepository.Remove(facture);
            await _factureAchatRepository.SaveChangesAsync();

            return NoContent();
        }

        private async Task<bool> FactureAchatExists(Guid id, Guid societeId)
        {
            return (await _factureAchatRepository.FindAsync(f => f.Id == id && f.SocieteId == societeId)).Any();
        }

        private Guid GetUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (userIdClaim == null || !Guid.TryParse(userIdClaim, out var userId))
            {
                throw new InvalidOperationException("User ID claim missing or invalid in JWT.");
            }
            return userId;
        }

        private string GetUserIP()
        {
            return HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
        }

        private string GetUserAgent()
        {
            return HttpContext.Request.Headers["User-Agent"].ToString();
        }
    }
}
