﻿using System.ComponentModel.DataAnnotations;
using System;

namespace stage.DTOs.User
{
    public class UtilisateurUpdateDto
    {
        [Required]
        public Guid Id { get; set; }

        [Required(ErrorMessage = "Le nom est requis.")]
        [StringLength(100, MinimumLength = 2, ErrorMessage = "Le nom doit avoir entre 2 et 100 caractères.")]
        public string Nom { get; set; }

        [Required(ErrorMessage = "L'adresse email est requise.")]
        [EmailAddress(ErrorMessage = "Format d'adresse email invalide.")]
        [StringLength(256, ErrorMessage = "L'email ne peut pas dépasser 256 caractères.")]
        public string Email { get; set; }

        [StringLength(100, MinimumLength = 6, ErrorMessage = "Le mot de passe doit avoir au moins 6 caractères.")]
        public string? MotDePasse { get; set; }

        [Required(ErrorMessage = "Le rôle est requis.")]
        [StringLength(50, ErrorMessage = "Le rôle ne peut pas dépasser 50 caractères.")]
        public string Role { get; set; }
    }
}