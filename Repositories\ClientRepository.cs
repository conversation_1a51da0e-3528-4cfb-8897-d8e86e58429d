using Microsoft.EntityFrameworkCore;
using stage.Models;

namespace stage.Repositories
{
    public class ClientRepository : BaseRepository<Client>, IClientRepository
    {
        public ClientRepository(StageContext context) : base(context)
        {
        }

        public async Task<IEnumerable<Client>> GetClientsBySocieteIdAsync(Guid societeId)
        {
            return await _dbSet
                .Include(c => c.Societe)
                .Where(c => c.SocieteId == societeId)
                .ToListAsync();
        }

        public async Task<Client?> GetClientByEmailAsync(string email, Guid societeId)
        {
            return await _dbSet
                .Include(c => c.Societe)
                .FirstOrDefaultAsync(c => c.Email == email && c.SocieteId == societeId);
        }

        public override async Task<Client?> GetByIdAsync(Guid id)
        {
            return await _dbSet
                .Include(c => c.Societe)
                .FirstOrDefaultAsync(c => c.Id == id);
        }
    }
}
