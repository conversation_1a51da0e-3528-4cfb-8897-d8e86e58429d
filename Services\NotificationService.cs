using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using stage.Models;
using System.Text.Json;

namespace stage.Services
{
    public class NotificationService : INotificationService
    {
        private readonly StageContext _context;
        private readonly ILogger<NotificationService> _logger;

        public NotificationService(StageContext context, ILogger<NotificationService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<Notification> CreateNotificationAsync(Guid utilisateurId, TypeNotification type, string titre, string message, string? lienAction = null, Dictionary<string, object>? metadata = null)
        {
            var utilisateur = await _context.Users.FindAsync(utilisateurId);
            if (utilisateur == null)
                throw new ArgumentException("Utilisateur non trouvé");

            var notification = new Notification
            {
                Id = Guid.NewGuid(),
                UtilisateurId = utilisateurId,
                SocieteId = utilisateur.SocieteId,
                Type = type,
                Titre = titre,
                Message = message,
                LienAction = lienAction,
                DonneesMetadata = metadata != null ? JsonSerializer.Serialize(metadata) : null,
                DateCreation = DateTime.UtcNow,
                Statut = StatutNotification.NonLue
            };

            _context.Notifications.Add(notification);
            await _context.SaveChangesAsync();

            _logger.LogInformation($"Notification créée pour l'utilisateur {utilisateurId}: {titre}");
            return notification;
        }

        public async Task<List<Notification>> GetUserNotificationsAsync(Guid utilisateurId, bool includeRead = true)
        {
            var query = _context.Notifications
                .Where(n => n.UtilisateurId == utilisateurId);

            if (!includeRead)
                query = query.Where(n => n.Statut == StatutNotification.NonLue);

            return await query
                .OrderByDescending(n => n.DateCreation)
                .Take(50) // Limiter à 50 notifications récentes
                .ToListAsync();
        }

        public async Task<List<Notification>> GetUnreadNotificationsAsync(Guid utilisateurId)
        {
            return await _context.Notifications
                .Where(n => n.UtilisateurId == utilisateurId && n.Statut == StatutNotification.NonLue)
                .OrderByDescending(n => n.DateCreation)
                .ToListAsync();
        }

        public async Task<bool> MarkAsReadAsync(Guid notificationId, Guid utilisateurId)
        {
            var notification = await _context.Notifications
                .FirstOrDefaultAsync(n => n.Id == notificationId && n.UtilisateurId == utilisateurId);

            if (notification == null)
                return false;

            notification.Statut = StatutNotification.Lue;
            notification.DateLecture = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> MarkAllAsReadAsync(Guid utilisateurId)
        {
            var notifications = await _context.Notifications
                .Where(n => n.UtilisateurId == utilisateurId && n.Statut == StatutNotification.NonLue)
                .ToListAsync();

            foreach (var notification in notifications)
            {
                notification.Statut = StatutNotification.Lue;
                notification.DateLecture = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeleteNotificationAsync(Guid notificationId, Guid utilisateurId)
        {
            var notification = await _context.Notifications
                .FirstOrDefaultAsync(n => n.Id == notificationId && n.UtilisateurId == utilisateurId);

            if (notification == null)
                return false;

            _context.Notifications.Remove(notification);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<int> GetUnreadCountAsync(Guid utilisateurId)
        {
            return await _context.Notifications
                .CountAsync(n => n.UtilisateurId == utilisateurId && n.Statut == StatutNotification.NonLue);
        }

        public async Task NotifyFactureCreatedAsync(Guid utilisateurId, Guid factureId, string numeroFacture)
        {
            // Récupérer l'utilisateur créateur pour obtenir sa société
            var utilisateurCreateur = await _context.Users.FindAsync(utilisateurId);
            if (utilisateurCreateur == null)
                throw new ArgumentException("Utilisateur créateur non trouvé");

            // Trouver tous les utilisateurs de la société
            var utilisateursSociete = await _context.Users
                .Where(u => u.SocieteId == utilisateurCreateur.SocieteId)
                .ToListAsync();

            // Notifier tous les utilisateurs de la société
            foreach (var utilisateur in utilisateursSociete)
            {
                var message = utilisateur.Id == utilisateurId
                    ? $"Vous avez créé la facture {numeroFacture} avec succès."
                    : $"Une nouvelle facture {numeroFacture} a été créée par {utilisateurCreateur.UserName ?? utilisateurCreateur.Email}.";

                await CreateNotificationAsync(
                    utilisateur.Id,
                    TypeNotification.FactureCreee,
                    "💰 Nouvelle facture créée",
                    message,
                    $"/factures/{factureId}",
                    new Dictionary<string, object>
                    {
                        { "factureId", factureId },
                        { "createurId", utilisateurId },
                        { "createurNom", utilisateurCreateur.UserName ?? utilisateurCreateur.Email }
                    }
                );
            }

            _logger.LogInformation($"Notification de création de facture {numeroFacture} envoyée à {utilisateursSociete.Count} utilisateur(s) de la société");
        }

        public async Task NotifyFactureStatusChangedAsync(Guid utilisateurId, Guid factureId, string numeroFacture, StatutFacture nouveauStatut)
        {
            var message = nouveauStatut switch
            {
                StatutFacture.Envoyee => $"La facture {numeroFacture} a été envoyée.",
                StatutFacture.Payee => $"La facture {numeroFacture} a été payée.",
                StatutFacture.EnRetard => $"La facture {numeroFacture} est en retard de paiement.",
                StatutFacture.Annulee => $"La facture {numeroFacture} a été annulée.",
                _ => $"Le statut de la facture {numeroFacture} a été modifié."
            };

            await CreateNotificationAsync(
                utilisateurId,
                TypeNotification.FactureEnvoyee, // Adapter selon le statut
                "Statut de facture modifié",
                message,
                $"/factures/{factureId}",
                new Dictionary<string, object> { { "factureId", factureId }, { "statut", nouveauStatut.ToString() } }
            );
        }

        public async Task NotifyDocumentStatusChangedAsync(Guid utilisateurId, Guid documentId, string titreDocument, StatutDocument nouveauStatut)
        {
            var (type, titre, message) = nouveauStatut switch
            {
                StatutDocument.Approuve => (TypeNotification.DocumentApprouve, "Document approuvé", $"Votre demande de document '{titreDocument}' a été approuvée."),
                StatutDocument.Rejete => (TypeNotification.DocumentRejete, "Document rejeté", $"Votre demande de document '{titreDocument}' a été rejetée."),
                StatutDocument.Genere => (TypeNotification.DocumentApprouve, "Document généré", $"Le document '{titreDocument}' a été généré et est prêt au téléchargement."),
                _ => (TypeNotification.DocumentDemande, "Statut de document modifié", $"Le statut du document '{titreDocument}' a été modifié.")
            };

            await CreateNotificationAsync(
                utilisateurId,
                type,
                titre,
                message,
                $"/documents/{documentId}",
                new Dictionary<string, object> { { "documentId", documentId }, { "statut", nouveauStatut.ToString() } }
            );
        }

        public async Task NotifyAdminDocumentRequestAsync(Guid societeId, Guid documentId, string titreDocument, string demandeurNom, bool estUrgent = false)
        {
            // Trouver tous les admins de la société
            var admins = await _context.Users
                .Where(u => u.SocieteId == societeId)
                .Join(_context.UserRoles, u => u.Id, ur => ur.UserId, (u, ur) => new { User = u, ur.RoleId })
                .Join(_context.Roles, x => x.RoleId, r => r.Id, (x, r) => new { x.User, Role = r })
                .Where(x => x.Role.Name == "Admin")
                .Select(x => x.User)
                .ToListAsync();

            foreach (var admin in admins)
            {
                var titre = estUrgent ? "🚨 Demande de document URGENTE" : "📄 Nouvelle demande de document";
                var message = estUrgent
                    ? $"URGENT: {demandeurNom} a fait une demande de document urgente: '{titreDocument}'"
                    : $"{demandeurNom} a fait une demande de document: '{titreDocument}'";

                await CreateNotificationAsync(
                    admin.Id,
                    TypeNotification.DocumentDemande,
                    titre,
                    message,
                    $"/documents/{documentId}",
                    new Dictionary<string, object>
                    {
                        { "documentId", documentId },
                        { "demandeurNom", demandeurNom },
                        { "estUrgent", estUrgent }
                    }
                );
            }

            _logger.LogInformation($"Notification envoyée à {admins.Count} admin(s) pour la demande de document: {titreDocument}");
        }

        public async Task NotifyUserCreatedAsync(Guid adminId, Guid newUserId, string newUserName)
        {
            await CreateNotificationAsync(
                adminId,
                TypeNotification.UtilisateurCree,
                "Nouvel utilisateur créé",
                $"L'utilisateur {newUserName} a été créé avec succès.",
                $"/utilisateurs/{newUserId}",
                new Dictionary<string, object> { { "userId", newUserId } }
            );
        }

        public async Task NotifyRetenueSourceAddedAsync(Guid utilisateurId, Guid retenueId)
        {
            await CreateNotificationAsync(
                utilisateurId,
                TypeNotification.RetenueSourceAjoutee,
                "Retenue à la source ajoutée",
                "Une nouvelle retenue à la source a été ajoutée avec succès.",
                $"/retenues/{retenueId}",
                new Dictionary<string, object> { { "retenueId", retenueId } }
            );
        }

        public async Task NotifyAdminsRetenueSourceAddedAsync(Guid societeId, Guid retenueId, string utilisateurNom)
        {
            // Trouver tous les admins de la société
            var admins = await _context.Users
                .Where(u => u.SocieteId == societeId)
                .Join(_context.UserRoles, u => u.Id, ur => ur.UserId, (u, ur) => new { User = u, ur.RoleId })
                .Join(_context.Roles, x => x.RoleId, r => r.Id, (x, r) => new { x.User, Role = r })
                .Where(x => x.Role.Name == "Admin")
                .Select(x => x.User)
                .ToListAsync();

            foreach (var admin in admins)
            {
                await CreateNotificationAsync(
                    admin.Id,
                    TypeNotification.RetenueSourceAjoutee,
                    "📄 Nouvelle retenue à la source",
                    $"{utilisateurNom} a ajouté une nouvelle retenue à la source.",
                    $"/retenues/{retenueId}",
                    new Dictionary<string, object>
                    {
                        { "retenueId", retenueId },
                        { "utilisateurNom", utilisateurNom }
                    }
                );
            }

            _logger.LogInformation($"Notification envoyée à {admins.Count} admin(s) pour la retenue ajoutée par: {utilisateurNom}");
        }

        public async Task SendSystemNotificationAsync(Guid societeId, string titre, string message)
        {
            var utilisateurs = await _context.Users
                .Where(u => u.SocieteId == societeId)
                .ToListAsync();

            foreach (var utilisateur in utilisateurs)
            {
                await CreateNotificationAsync(
                    utilisateur.Id,
                    TypeNotification.Systeme,
                    titre,
                    message
                );
            }
        }
    }
}
