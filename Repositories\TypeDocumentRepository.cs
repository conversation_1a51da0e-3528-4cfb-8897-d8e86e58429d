using Microsoft.EntityFrameworkCore;
using stage.Models;

namespace stage.Repositories
{
    public class TypeDocumentRepository : BaseRepository<TypeDocument>, ITypeDocumentRepository
    {
        public TypeDocumentRepository(StageContext context) : base(context)
        {
        }

        public async Task<TypeDocument?> GetTypeDocumentByNomAsync(string nom)
        {
            return await _dbSet
                .Include(t => t.Documents)
                .FirstOrDefaultAsync(t => t.Nom == nom);
        }

        public override async Task<TypeDocument?> GetByIdAsync(Guid id)
        {
            return await _dbSet
                .Include(t => t.Documents)
                .FirstOrDefaultAsync(t => t.Id == id);
        }
    }
}
