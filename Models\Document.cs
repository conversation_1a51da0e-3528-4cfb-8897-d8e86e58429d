namespace stage.Models
{
    public enum StatutDocument
    {
        EnAttente = 0,
        Genere = 1,
        Approuve = 2,
        Rejete = 3,
        Archive = 4
    }

    public enum TypeDemandeDocument
    {
        AttestationTravail = 0,
        AttestationStage = 1,
        CertificatFormation = 2,
        Autre = 3
    }

    public class Document
    {
        public Guid Id { get; set; }
        public string Titre { get; set; }
        public string Contenu { get; set; }
        public DateTime DateCreation { get; set; }
        public DateTime? DateModification { get; set; }
        public StatutDocument Statut { get; set; } = StatutDocument.EnAttente;
        public TypeDemandeDocument TypeDemande { get; set; }
        public bool EstUrgent { get; set; } = false;
        public string? EmailCopie { get; set; }
        public string? CommentairesApprobation { get; set; }
        public Guid TypeDocumentId { get; set; }
        public TypeDocument TypeDocument { get; set; }
        public Guid SocieteId { get; set; }
        public Societe Societe { get; set; }
        public Guid? UtilisateurDemandeurId { get; set; }
        public Applicationuser? UtilisateurDemandeur { get; set; }
        public Guid? UtilisateurApprobateurId { get; set; }
        public Applicationuser? UtilisateurApprobateur { get; set; }
        public DateTime? DateApprobation { get; set; }
    }
}