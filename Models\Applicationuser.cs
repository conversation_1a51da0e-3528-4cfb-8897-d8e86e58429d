﻿using Microsoft.AspNetCore.Identity;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace stage.Models
{
    public class Applicationuser : IdentityUser<Guid>
    {
        [StringLength(100)]
        public string Nom { get; set; } 

        public Guid SocieteId { get; set; }

        [ForeignKey("SocieteId")]
        public Societe Societe { get; set; }
    }
}