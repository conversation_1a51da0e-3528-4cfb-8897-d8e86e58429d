﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using stage.Models;

#nullable disable

namespace stage.Migrations
{
    [DbContext(typeof(StageContext))]
    partial class StageContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole<System.Guid>", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex")
                        .HasFilter("[NormalizedName] IS NOT NULL");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<System.Guid>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<System.Guid>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<System.Guid>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<System.Guid>", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<System.Guid>", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("stage.Models.Applicationuser", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("SocieteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex")
                        .HasFilter("[NormalizedUserName] IS NOT NULL");

                    b.HasIndex("SocieteId");

                    b.ToTable("AspNetUsers", (string)null);
                });

            modelBuilder.Entity("stage.Models.AuditLog", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Action")
                        .HasColumnType("int");

                    b.Property<string>("AdresseIP")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateAction")
                        .HasColumnType("datetime2");

                    b.Property<string>("Details")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("EntiteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("NomEntite")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("SocieteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("TypeEntite")
                        .HasColumnType("int");

                    b.Property<string>("UserAgent")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("UtilisateurId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ValeurApres")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ValeurAvant")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("SocieteId");

                    b.HasIndex("UtilisateurId");

                    b.ToTable("AuditLogs");
                });

            modelBuilder.Entity("stage.Models.Client", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Adresse")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("ChiffreAffaireTotal")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DernierePaiement")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("NombreFactures")
                        .HasColumnType("int");

                    b.Property<string>("NotesInternes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("SocieteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Statut")
                        .HasColumnType("int");

                    b.Property<string>("Telephone")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TelephoneMobile")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("SocieteId");

                    b.ToTable("Clients");
                });

            modelBuilder.Entity("stage.Models.Document", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CommentairesApprobation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Contenu")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DateApprobation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<string>("EmailCopie")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("EstUrgent")
                        .HasColumnType("bit");

                    b.Property<Guid>("SocieteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Statut")
                        .HasColumnType("int");

                    b.Property<string>("Titre")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("TypeDemande")
                        .HasColumnType("int");

                    b.Property<Guid>("TypeDocumentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UtilisateurApprobateurId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UtilisateurDemandeurId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("SocieteId");

                    b.HasIndex("TypeDocumentId");

                    b.HasIndex("UtilisateurApprobateurId");

                    b.HasIndex("UtilisateurDemandeurId");

                    b.ToTable("Documents");
                });

            modelBuilder.Entity("stage.Models.FactureAchat", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateEcheance")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateEnvoi")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DatePaiement")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("FournisseurId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("MatriculeFiscaleSociete")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("Montant")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("NotesInternes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Numero")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("SocieteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Statut")
                        .HasColumnType("int");

                    b.Property<Guid?>("UtilisateurCreationId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("FournisseurId");

                    b.HasIndex("SocieteId");

                    b.HasIndex("UtilisateurCreationId");

                    b.ToTable("FacturesAchat");
                });

            modelBuilder.Entity("stage.Models.FactureVente", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ClientId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateEcheance")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateEnvoi")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DatePaiement")
                        .HasColumnType("datetime2");

                    b.Property<string>("MatriculeFiscaleSociete")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("Montant")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("NotesInternes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Numero")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("SocieteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Statut")
                        .HasColumnType("int");

                    b.Property<Guid?>("UtilisateurCreationId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ClientId");

                    b.HasIndex("SocieteId");

                    b.HasIndex("UtilisateurCreationId");

                    b.ToTable("FacturesVente");
                });

            modelBuilder.Entity("stage.Models.Fournisseur", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Adresse")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Categorie")
                        .HasColumnType("int");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DerniereCommande")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("MontantTotalCommandes")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("NombreCommandes")
                        .HasColumnType("int");

                    b.Property<string>("NotesInternes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("SocieteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Statut")
                        .HasColumnType("int");

                    b.Property<string>("Telephone")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TelephoneMobile")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("SocieteId");

                    b.ToTable("Fournisseurs");
                });

            modelBuilder.Entity("stage.Models.Notification", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ClientId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateLecture")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("DocumentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("DonneesMetadata")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("FactureId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("FournisseurId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("LienAction")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("SocieteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Statut")
                        .HasColumnType("int");

                    b.Property<string>("Titre")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<Guid>("UtilisateurId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("SocieteId");

                    b.HasIndex("UtilisateurId");

                    b.ToTable("Notifications");
                });

            modelBuilder.Entity("stage.Models.NotificationPreference", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Canal")
                        .HasColumnType("int");

                    b.Property<bool>("EstActive")
                        .HasColumnType("bit");

                    b.Property<int>("TypeNotification")
                        .HasColumnType("int");

                    b.Property<Guid>("UtilisateurId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("UtilisateurId");

                    b.ToTable("NotificationPreferences");
                });

            modelBuilder.Entity("stage.Models.ProfilUtilisateur", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Adresse")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("AlertesConnexion")
                        .HasColumnType("bit");

                    b.Property<string>("CachetNumerique")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateEmbauche")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<string>("Departement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("DeuxFacteursActive")
                        .HasColumnType("bit");

                    b.Property<string>("DevisePreferee")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FormatDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LanguePreferee")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhotoProfil")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Poste")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SignatureNumerique")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Telephone")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TelephoneMobile")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ThemeInterface")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("TimeoutSession")
                        .HasColumnType("int");

                    b.Property<Guid>("UtilisateurId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("UtilisateurId")
                        .IsUnique();

                    b.ToTable("ProfilsUtilisateurs");
                });

            modelBuilder.Entity("stage.Models.RetenueSource", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CheminFichier")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Commentaires")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DateScan")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("SocieteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("UtilisateurId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("SocieteId");

                    b.HasIndex("UtilisateurId");

                    b.ToTable("RetenuesSource");
                });

            modelBuilder.Entity("stage.Models.Societe", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Adresse")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Cachet")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Logo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MatriculeFiscale")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Signature")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Telephone")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("MatriculeFiscale")
                        .IsUnique();

                    b.ToTable("Societes");
                });

            modelBuilder.Entity("stage.Models.TypeDocument", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("SocieteId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("SocieteId");

                    b.ToTable("TypeDocuments");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<System.Guid>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole<System.Guid>", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<System.Guid>", b =>
                {
                    b.HasOne("stage.Models.Applicationuser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<System.Guid>", b =>
                {
                    b.HasOne("stage.Models.Applicationuser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<System.Guid>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole<System.Guid>", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("stage.Models.Applicationuser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<System.Guid>", b =>
                {
                    b.HasOne("stage.Models.Applicationuser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("stage.Models.Applicationuser", b =>
                {
                    b.HasOne("stage.Models.Societe", "Societe")
                        .WithMany("Utilisateurs")
                        .HasForeignKey("SocieteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Societe");
                });

            modelBuilder.Entity("stage.Models.AuditLog", b =>
                {
                    b.HasOne("stage.Models.Societe", "Societe")
                        .WithMany()
                        .HasForeignKey("SocieteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("stage.Models.Applicationuser", "Utilisateur")
                        .WithMany()
                        .HasForeignKey("UtilisateurId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Societe");

                    b.Navigation("Utilisateur");
                });

            modelBuilder.Entity("stage.Models.Client", b =>
                {
                    b.HasOne("stage.Models.Societe", "Societe")
                        .WithMany("Clients")
                        .HasForeignKey("SocieteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Societe");
                });

            modelBuilder.Entity("stage.Models.Document", b =>
                {
                    b.HasOne("stage.Models.Societe", "Societe")
                        .WithMany("Documents")
                        .HasForeignKey("SocieteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("stage.Models.TypeDocument", "TypeDocument")
                        .WithMany("Documents")
                        .HasForeignKey("TypeDocumentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("stage.Models.Applicationuser", "UtilisateurApprobateur")
                        .WithMany()
                        .HasForeignKey("UtilisateurApprobateurId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("stage.Models.Applicationuser", "UtilisateurDemandeur")
                        .WithMany()
                        .HasForeignKey("UtilisateurDemandeurId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Societe");

                    b.Navigation("TypeDocument");

                    b.Navigation("UtilisateurApprobateur");

                    b.Navigation("UtilisateurDemandeur");
                });

            modelBuilder.Entity("stage.Models.FactureAchat", b =>
                {
                    b.HasOne("stage.Models.Fournisseur", "Fournisseur")
                        .WithMany("FacturesAchat")
                        .HasForeignKey("FournisseurId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("stage.Models.Societe", "Societe")
                        .WithMany("FacturesAchat")
                        .HasForeignKey("SocieteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("stage.Models.Applicationuser", "UtilisateurCreation")
                        .WithMany()
                        .HasForeignKey("UtilisateurCreationId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Fournisseur");

                    b.Navigation("Societe");

                    b.Navigation("UtilisateurCreation");
                });

            modelBuilder.Entity("stage.Models.FactureVente", b =>
                {
                    b.HasOne("stage.Models.Client", "Client")
                        .WithMany("FacturesVente")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("stage.Models.Societe", "Societe")
                        .WithMany("FacturesVente")
                        .HasForeignKey("SocieteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("stage.Models.Applicationuser", "UtilisateurCreation")
                        .WithMany()
                        .HasForeignKey("UtilisateurCreationId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Client");

                    b.Navigation("Societe");

                    b.Navigation("UtilisateurCreation");
                });

            modelBuilder.Entity("stage.Models.Fournisseur", b =>
                {
                    b.HasOne("stage.Models.Societe", "Societe")
                        .WithMany("Fournisseurs")
                        .HasForeignKey("SocieteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Societe");
                });

            modelBuilder.Entity("stage.Models.Notification", b =>
                {
                    b.HasOne("stage.Models.Societe", "Societe")
                        .WithMany()
                        .HasForeignKey("SocieteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("stage.Models.Applicationuser", "Utilisateur")
                        .WithMany()
                        .HasForeignKey("UtilisateurId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Societe");

                    b.Navigation("Utilisateur");
                });

            modelBuilder.Entity("stage.Models.NotificationPreference", b =>
                {
                    b.HasOne("stage.Models.Applicationuser", "Utilisateur")
                        .WithMany()
                        .HasForeignKey("UtilisateurId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Utilisateur");
                });

            modelBuilder.Entity("stage.Models.ProfilUtilisateur", b =>
                {
                    b.HasOne("stage.Models.Applicationuser", "Utilisateur")
                        .WithOne()
                        .HasForeignKey("stage.Models.ProfilUtilisateur", "UtilisateurId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Utilisateur");
                });

            modelBuilder.Entity("stage.Models.RetenueSource", b =>
                {
                    b.HasOne("stage.Models.Societe", "Societe")
                        .WithMany("RetenuesSource")
                        .HasForeignKey("SocieteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("stage.Models.Applicationuser", "Utilisateur")
                        .WithMany()
                        .HasForeignKey("UtilisateurId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Societe");

                    b.Navigation("Utilisateur");
                });

            modelBuilder.Entity("stage.Models.TypeDocument", b =>
                {
                    b.HasOne("stage.Models.Societe", "Societe")
                        .WithMany()
                        .HasForeignKey("SocieteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Societe");
                });

            modelBuilder.Entity("stage.Models.Client", b =>
                {
                    b.Navigation("FacturesVente");
                });

            modelBuilder.Entity("stage.Models.Fournisseur", b =>
                {
                    b.Navigation("FacturesAchat");
                });

            modelBuilder.Entity("stage.Models.Societe", b =>
                {
                    b.Navigation("Clients");

                    b.Navigation("Documents");

                    b.Navigation("FacturesAchat");

                    b.Navigation("FacturesVente");

                    b.Navigation("Fournisseurs");

                    b.Navigation("RetenuesSource");

                    b.Navigation("Utilisateurs");
                });

            modelBuilder.Entity("stage.Models.TypeDocument", b =>
                {
                    b.Navigation("Documents");
                });
#pragma warning restore 612, 618
        }
    }
}
