﻿using Microsoft.AspNetCore.Mvc;
using stage.Models;
using stage.Repositories;
using stage.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;
using Microsoft.EntityFrameworkCore;

namespace stage.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class DocumentsController : ControllerBase
    {
        private readonly IDocumentRepository _documentRepository;
        private readonly ITypeDocumentRepository _typeDocumentRepository;
        private readonly IFileService _fileService;
        private readonly INotificationService _notificationService;
        private readonly IAuditService _auditService;

        public DocumentsController(
            IDocumentRepository documentRepository,
            ITypeDocumentRepository typeDocumentRepository,
            IFileService fileService,
            INotificationService notificationService,
            IAuditService auditService)
        {
            _documentRepository = documentRepository;
            _typeDocumentRepository = typeDocumentRepository;
            _fileService = fileService;
            _notificationService = notificationService;
            _auditService = auditService;
        }

        private Guid GetUserSocieteId()
        {
            var societeIdClaim = User.FindFirst("SocieteId");
            if (societeIdClaim == null || !Guid.TryParse(societeIdClaim.Value, out var societeId))
            {
                throw new InvalidOperationException("SocieteId claim missing or invalid in JWT. User not properly authenticated.");
            }
            return societeId;
        }

        private Guid GetUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
            {
                throw new InvalidOperationException("UserId claim missing or invalid in JWT.");
            }
            return userId;
        }

        private string GetUserIP()
        {
            return HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
        }

        private string GetUserAgent()
        {
            return HttpContext.Request.Headers["User-Agent"].ToString();
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<Document>>> GetDocuments()
        {
            var societeId = GetUserSocieteId();
            var userId = GetUserId();

            // Log de consultation (Admin uniquement)
            if (User.IsInRole("Admin"))
            {
                await _auditService.LogConsultationAsync(
                    TypeEntite.Document,
                    Guid.Empty,
                    "Liste des documents",
                    userId,
                    societeId,
                    GetUserIP(),
                    GetUserAgent()
                );
            }

            var documents = await _documentRepository.GetQueryable()
                .Include(d => d.TypeDocument)
                .Include(d => d.Societe)
                .Include(d => d.UtilisateurDemandeur)
                .Include(d => d.UtilisateurApprobateur)
                .Where(d => d.SocieteId == societeId)
                .OrderByDescending(d => d.DateCreation)
                .ToListAsync();

            return Ok(documents);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<Document>> GetDocument(Guid id)
        {
            var societeId = GetUserSocieteId();
        
            var document = await _documentRepository.GetQueryable()
                                                     .Include(d => d.TypeDocument)
                                                     .Include(d => d.Societe) 
                                                     .FirstOrDefaultAsync(d => d.Id == id);

            if (document == null || document.SocieteId != societeId)
            {
                return NotFound();
            }

            return Ok(document);
        }

        [HttpPost]
        public async Task<ActionResult<Document>> PostDocument([FromBody] Document document)
        {
            
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var societeId = GetUserSocieteId();

          
            var typeDoc = await _typeDocumentRepository.GetByIdAsync(document.TypeDocumentId);
            if (typeDoc == null || typeDoc.SocieteId != societeId)
            {
                return BadRequest("Le type de document spécifié n'existe pas ou n'appartient pas à votre société.");
            }

          
            document.Id = Guid.NewGuid();
            document.SocieteId = societeId; 
            document.DateCreation = DateTime.UtcNow; 

            
            document.TypeDocument = null;
            document.Societe = null;

            await _documentRepository.AddAsync(document);
            await _documentRepository.SaveChangesAsync();

            // Notification aux admins pour la nouvelle demande
            var userName = User.FindFirst(ClaimTypes.Name)?.Value ?? User.FindFirst(ClaimTypes.Email)?.Value ?? "Utilisateur";
            await _notificationService.NotifyAdminDocumentRequestAsync(
                societeId,
                document.Id,
                document.Titre,
                userName,
                document.EstUrgent
            );

            var createdDocument = await _documentRepository.GetQueryable()
                                                            .Include(d => d.TypeDocument)
                                                            .Include(d => d.Societe)
                                                            .FirstOrDefaultAsync(d => d.Id == document.Id);

            return CreatedAtAction(nameof(GetDocument), new { id = createdDocument.Id }, createdDocument);
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> PutDocument(Guid id, [FromBody] Document document)
        {
            
            if (id != document.Id || !ModelState.IsValid)
            {
                return BadRequest();
            }

            var societeId = GetUserSocieteId();
            var existingDocument = await _documentRepository.GetByIdAsync(id);

            
            if (existingDocument == null || existingDocument.SocieteId != societeId)
            {
                return NotFound();
            }

          
            var typeDoc = await _typeDocumentRepository.GetByIdAsync(document.TypeDocumentId);
            if (typeDoc == null || typeDoc.SocieteId != societeId)
            {
                return BadRequest("Le type de document spécifié n'existe pas ou n'appartient pas à votre société.");
            }

        
            existingDocument.Titre = document.Titre;
            existingDocument.Contenu = document.Contenu;
            existingDocument.TypeDocumentId = document.TypeDocumentId;
           

            _documentRepository.Update(existingDocument);

            try
            {
                await _documentRepository.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!await DocumentExists(id, societeId))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteDocument(Guid id)
        {
            var societeId = GetUserSocieteId();
            var document = await _documentRepository.GetByIdAsync(id);

            if (document == null || document.SocieteId != societeId)
            {
                return NotFound();
            }

            _documentRepository.Remove(document);
            await _documentRepository.SaveChangesAsync();

            return NoContent();
        }

        [HttpPost("{id}/approve")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> ApproveDocument(Guid id, [FromBody] string? commentaires = null)
        {
            var societeId = GetUserSocieteId();
            var userId = GetUserId();

            var document = await _documentRepository.GetByIdAsync(id);
            if (document == null || document.SocieteId != societeId)
                return NotFound();

            document.Statut = StatutDocument.Approuve;
            document.UtilisateurApprobateurId = userId;
            document.DateApprobation = DateTime.UtcNow;
            document.CommentairesApprobation = commentaires;
            document.DateModification = DateTime.UtcNow;

            _documentRepository.Update(document);
            await _documentRepository.SaveChangesAsync();

            // Notification
            if (document.UtilisateurDemandeurId.HasValue)
            {
                await _notificationService.NotifyDocumentStatusChangedAsync(
                    document.UtilisateurDemandeurId.Value,
                    document.Id,
                    document.Titre,
                    StatutDocument.Approuve
                );
            }

            // Audit
            await _auditService.LogActionAsync(
                TypeAction.ChangementStatut,
                TypeEntite.Document,
                document.Id,
                document.Titre,
                userId,
                societeId,
                "Approbation du document",
                null,
                new { Statut = StatutDocument.Approuve, Commentaires = commentaires },
                GetUserIP(),
                GetUserAgent()
            );

            return Ok(new { Message = "Document approuvé avec succès" });
        }

        [HttpPost("{id}/reject")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> RejectDocument(Guid id, [FromBody] string commentaires)
        {
            var societeId = GetUserSocieteId();
            var userId = GetUserId();

            var document = await _documentRepository.GetByIdAsync(id);
            if (document == null || document.SocieteId != societeId)
                return NotFound();

            document.Statut = StatutDocument.Rejete;
            document.UtilisateurApprobateurId = userId;
            document.DateApprobation = DateTime.UtcNow;
            document.CommentairesApprobation = commentaires;
            document.DateModification = DateTime.UtcNow;

            _documentRepository.Update(document);
            await _documentRepository.SaveChangesAsync();

            // Notification
            if (document.UtilisateurDemandeurId.HasValue)
            {
                await _notificationService.NotifyDocumentStatusChangedAsync(
                    document.UtilisateurDemandeurId.Value,
                    document.Id,
                    document.Titre,
                    StatutDocument.Rejete
                );
            }

            // Audit
            await _auditService.LogActionAsync(
                TypeAction.ChangementStatut,
                TypeEntite.Document,
                document.Id,
                document.Titre,
                userId,
                societeId,
                "Rejet du document",
                null,
                new { Statut = StatutDocument.Rejete, Commentaires = commentaires },
                GetUserIP(),
                GetUserAgent()
            );

            return Ok(new { Message = "Document rejeté" });
        }



        private async Task<bool> DocumentExists(Guid id, Guid societeId)
        {
            return (await _documentRepository.FindAsync(d => d.Id == id && d.SocieteId == societeId)).Any();
        }
    }
}