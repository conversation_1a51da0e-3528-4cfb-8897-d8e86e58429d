﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using stage.DTOs.Auth;
using stage.Services;
using System.Threading.Tasks;

namespace stage.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AuthController : ControllerBase
    {
        private readonly IAuthService _authService;

        public AuthController(IAuthService authService)
        {
            _authService = authService;
        }

       
        
        [HttpPost("register")]
        public async Task<IActionResult> Register([FromBody] UserRegisterDto registerDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var response = await _authService.RegisterAdminAndSociete(registerDto);

            if (response == null)
            {
               
                return Conflict("Échec de l'inscription. L'adresse email est peut-être déjà utilisée ou une autre erreur est survenue.");
            }

            return Ok(response);
        }


        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody] UserLoginDto loginDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var response = await _authService.Login(loginDto);

            if (response == null)
            {
                return Unauthorized("Email ou mot de passe invalide.");
            }

            return Ok(response);
        }

        
        [HttpPost("logout")]
        [Authorize]
        public async Task<IActionResult> Logout()
        {
            var response = await _authService.LogoutAsync();
            return Ok(response);
        }
    }
}