﻿namespace stage.Models
{
    public enum StatutFacture
    {
        Brouillon = 0,
        Envoyee = 1,
        Payee = 2,
        EnRetard = 3,
        Annulee = 4
    }

    public abstract class Facture
    {
        public Guid Id { get; set; }
        public string Numero { get; set; }
        public decimal Montant { get; set; }
        public DateTime Date { get; set; }
        public DateTime? DateEcheance { get; set; }
        public StatutFacture Statut { get; set; } = StatutFacture.Brouillon;
        public DateTime? DateEnvoi { get; set; }
        public DateTime? DatePaiement { get; set; }
        public string? NotesInternes { get; set; }
        public string MatriculeFiscaleSociete { get; set; }
        public Guid SocieteId { get; set; }
        public Societe Societe { get; set; }
        public Guid? UtilisateurCreationId { get; set; }
        public Applicationuser? UtilisateurCreation { get; set; }
        public DateTime DateCreation { get; set; } = DateTime.UtcNow;
        public DateTime? DateModification { get; set; }
    }
}