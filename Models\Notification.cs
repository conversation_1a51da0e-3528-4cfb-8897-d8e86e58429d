namespace stage.Models
{
    public enum TypeNotification
    {
        FactureCreee = 0,
        FactureEnvoyee = 1,
        FacturePayee = 2,
        FactureEnRetard = 3,
        DocumentDemande = 4,
        DocumentApprouve = 5,
        DocumentRejete = 6,
        UtilisateurCree = 7,
        RetenueSourceAjoutee = 8,
        Systeme = 9
    }

    public enum StatutNotification
    {
        NonLue = 0,
        Lue = 1,
        Archivee = 2
    }

    public class Notification
    {
        public Guid Id { get; set; }
        public string Titre { get; set; }
        public string Message { get; set; }
        public TypeNotification Type { get; set; }
        public StatutNotification Statut { get; set; } = StatutNotification.NonLue;
        public DateTime DateCreation { get; set; } = DateTime.UtcNow;
        public DateTime? DateLecture { get; set; }
        public string? LienAction { get; set; }
        public string? DonneesMetadata { get; set; } // JSON pour données supplémentaires

        // Relations
        public Guid UtilisateurId { get; set; }
        public Applicationuser Utilisateur { get; set; }
        public Guid SocieteId { get; set; }
        public Societe Societe { get; set; }

        // Références optionnelles vers les entités liées
        public Guid? FactureId { get; set; }
        public Guid? DocumentId { get; set; }
        public Guid? ClientId { get; set; }
        public Guid? FournisseurId { get; set; }
    }
}
